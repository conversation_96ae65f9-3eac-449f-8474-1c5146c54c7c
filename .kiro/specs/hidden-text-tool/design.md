# 隐藏文字工具设计文档

## 概述

隐藏文字工具是一个基于微信小程序Canvas 2D API的图形生成应用。系统采用模块化架构，将文字渲染、图案生成、用户交互和图片导出等功能分离，确保代码的可维护性和扩展性。

核心设计理念：
- **实时渲染**：所有参数变更立即反映在画布上
- **插件化图案系统**：支持动态加载和配置不同的干扰线图案
- **响应式设计**：适配不同设备屏幕尺寸和像素密度
- **高性能**：优化渲染流程，确保流畅的用户体验

## 架构

### 系统架构图

```mermaid
graph TB
    A[用户界面层] --> B[控制器层]
    B --> C[渲染引擎]
    B --> D[图案管理器]
    B --> E[颜色管理器]
    B --> F[导出管理器]
    
    C --> G[Canvas 2D API]
    D --> H[图案插件系统]
    F --> I[微信API]
    
    subgraph "数据层"
        J[文字配置]
        K[图案配置]
        L[颜色配置]
        M[画布状态]
    end
    
    B --> J
    B --> K
    B --> L
    B --> M
```

### 分层架构

1. **用户界面层（UI Layer）**
   - WXML模板：定义用户界面结构
   - WXSS样式：控制界面外观和布局
   - 用户交互事件处理

2. **控制器层（Controller Layer）**
   - Page对象：微信小程序页面控制器
   - 事件处理：用户操作响应
   - 状态管理：维护应用状态

3. **业务逻辑层（Business Logic Layer）**
   - 渲染引擎：Canvas绘制逻辑
   - 图案管理器：干扰线图案系统
   - 颜色管理器：颜色选择和管理
   - 导出管理器：图片生成和保存

4. **数据访问层（Data Access Layer）**
   - 配置数据：文字、图案、颜色等配置
   - 状态数据：画布状态、用户交互状态

## 组件和接口

### 核心组件

#### 1. 渲染引擎（RenderEngine）

**职责**：负责Canvas内容的绘制和更新

```javascript
class RenderEngine {
  constructor(canvas, context) {
    this.canvas = canvas;
    this.ctx = context;
  }
  
  // 主渲染方法
  render(config) {
    this.clearCanvas();
    this.drawBackground(config.backgroundColor);
    this.drawText(config.textConfig);
    if (config.enableInterference) {
      this.drawInterferencePattern(config.patternConfig);
    }
  }
  
  // 清空画布
  clearCanvas() { /* ... */ }
  
  // 绘制背景
  drawBackground(color) { /* ... */ }
  
  // 绘制文字
  drawText(textConfig) { /* ... */ }
  
  // 绘制干扰线
  drawInterferencePattern(patternConfig) { /* ... */ }
}
```

#### 2. 图案管理器（PatternManager）

**职责**：管理干扰线图案的创建、配置和渲染

```javascript
class PatternManager {
  constructor() {
    this.patterns = new Map();
    this.loadPatterns();
  }
  
  // 获取所有图案信息
  getAllPatternsInfo() { /* ... */ }
  
  // 创建图案实例
  create(patternKey, params) { /* ... */ }
  
  // 注册新图案
  register(patternKey, patternClass) { /* ... */ }
}

// 图案基类
class BasePattern {
  constructor(options) {
    this.options = options;
  }
  
  // 更新参数
  updateOptions(newOptions) { /* ... */ }
  
  // 绘制图案
  draw(ctx, width, height, margin) { /* ... */ }
  
  // 获取配置信息
  static getConfig() { /* ... */ }
}
```

#### 3. 颜色管理器（ColorManager）

**职责**：管理颜色选择和预设颜色

```javascript
class ColorManager {
  constructor() {
    this.presetColors = [
      '#000000', '#333333', '#666666', // ... 30种预设颜色
    ];
  }
  
  // 获取预设颜色
  getPresetColors() { /* ... */ }
  
  // 验证颜色格式
  validateColor(color) { /* ... */ }
  
  // 颜色格式转换
  convertColor(color, format) { /* ... */ }
}
```

#### 4. 导出管理器（ExportManager）

**职责**：处理图片导出和保存功能

```javascript
class ExportManager {
  constructor(canvas) {
    this.canvas = canvas;
  }
  
  // 导出图片
  async exportImage(format, quality, pixelRatio) {
    return new Promise((resolve, reject) => {
      wx.canvasToTempFilePath({
        canvas: this.canvas,
        fileType: format,
        quality: quality,
        destWidth: this.canvas.width,
        destHeight: this.canvas.height,
        success: resolve,
        fail: reject
      });
    });
  }
  
  // 保存到相册
  async saveToAlbum(tempFilePath) { /* ... */ }
}
```

### 接口定义

#### 文字配置接口

```typescript
interface TextConfig {
  content: string;           // 文字内容
  fontSize: number;          // 字体大小 (14-128)
  lineHeight: number;        // 行间距 (1-3)
  letterSpacing: number;     // 字间距 (0-10)
  color: string;            // 文字颜色
  isBold: boolean;          // 是否加粗
  isBlur: boolean;          // 是否模糊
  alignIndex: number;       // 对齐方式 (0:左, 1:中, 2:右)
  x: number;                // X坐标
  y: number;                // Y坐标
}
```

#### 图案配置接口

```typescript
interface PatternConfig {
  key: string;              // 图案标识
  name: string;             // 图案名称
  enabled: boolean;         // 是否启用
  color: string;            // 线条颜色
  params: Record<string, any>; // 动态参数
  config: PatternParamConfig[]; // 参数配置
}

interface PatternParamConfig {
  key: string;              // 参数键
  label: string;            // 显示标签
  type: 'slider' | 'picker' | 'switch' | 'color'; // 参数类型
  min?: number;             // 最小值（slider）
  max?: number;             // 最大值（slider）
  step?: number;            // 步长（slider）
  options?: string[];       // 选项（picker）
  values?: any[];           // 值列表（picker）
  default: any;             // 默认值
}
```

#### 画布配置接口

```typescript
interface CanvasConfig {
  width: number;            // 画布宽度
  height: number;           // 画布高度
  pixelRatio: number;       // 像素比
  backgroundColor: string;   // 背景颜色
}
```

## 数据模型

### 应用状态模型

```javascript
const AppState = {
  // 文字设置
  textConfig: {
    content: '这是一段\n隐藏文字\n可以输入\n多行内容',
    fontSize: 50,
    lineHeight: 1.5,
    letterSpacing: 2,
    color: '#000000',
    isBold: true,
    isBlur: false,
    alignIndex: 1,
    x: 187.5,
    y: 187.5
  },
  
  // 画布设置
  canvasConfig: {
    width: 375,
    height: 375,
    pixelRatio: 2,
    backgroundColor: '#ffffff'
  },
  
  // 图案设置
  patternConfig: {
    enabled: true,
    currentKey: 'zigzag',
    color: '#000000',
    params: {},
    availablePatterns: []
  },
  
  // 导出设置
  exportConfig: {
    format: 'png',
    quality: 0.9
  },
  
  // 交互状态
  interactionState: {
    isDragging: false,
    lastTouchX: 0,
    lastTouchY: 0
  },
  
  // 系统信息
  systemInfo: {
    screenWidth: 375,
    screenHeight: 667,
    statusBarHeight: 20,
    navHeight: 66
  }
};
```

### 图案插件模型

```javascript
// 锯齿线图案示例
class ZigzagPattern extends BasePattern {
  static getConfig() {
    return {
      key: 'zigzag',
      name: '锯齿线',
      config: [
        {
          key: 'density',
          label: '密度',
          type: 'slider',
          min: 5,
          max: 50,
          step: 1,
          default: 20
        },
        {
          key: 'amplitude',
          label: '幅度',
          type: 'slider',
          min: 1,
          max: 20,
          step: 1,
          default: 5
        },
        {
          key: 'direction',
          label: '方向',
          type: 'picker',
          options: ['水平', '垂直', '对角'],
          values: ['horizontal', 'vertical', 'diagonal'],
          default: 'horizontal'
        }
      ]
    };
  }
  
  draw(ctx, width, height, margin) {
    const { density, amplitude, direction, lineColor } = this.options;
    ctx.strokeStyle = lineColor;
    ctx.lineWidth = 1;
    
    // 根据方向和参数绘制锯齿线
    this.drawZigzagLines(ctx, width, height, density, amplitude, direction);
  }
}
```

## 错误处理

### 错误分类和处理策略

#### 1. 系统初始化错误

```javascript
// 设备信息获取失败
try {
  const windowInfo = wx.getWindowInfo();
  const deviceInfo = wx.getDeviceInfo();
  // 使用获取的信息
} catch (error) {
  console.error('获取系统信息失败:', error);
  // 使用默认配置继续运行
  this.setData({
    statusBarHeight: 20,
    navHeight: 66,
    screenWidth: 375,
    pixelRatio: 2
  });
}
```

#### 2. Canvas初始化错误

```javascript
// Canvas节点获取失败
query.select('#hiddenTextCanvas')
  .fields({ node: true, size: true })
  .exec((res) => {
    if (!res || !res[0] || !res[0].node) {
      console.error('获取Canvas节点失败');
      wx.showModal({
        title: '初始化失败',
        content: '画布初始化失败，请重新进入页面',
        showCancel: false
      });
      return;
    }
    // 正常初始化流程
  });
```

#### 3. 图片导出错误

```javascript
// 导出失败处理
onExport() {
  if (!this.canvas) {
    wx.showToast({
      title: '画布未准备好',
      icon: 'error'
    });
    return;
  }
  
  wx.canvasToTempFilePath({
    canvas: this.canvas,
    // ... 其他参数
    success: (res) => {
      this.saveToAlbum(res.tempFilePath);
    },
    fail: (err) => {
      console.error('导出失败:', err);
      wx.showModal({
        title: '导出失败',
        content: '图片生成失败，请重试',
        showCancel: false
      });
    }
  });
}

// 保存到相册失败处理
saveToAlbum(filePath) {
  wx.saveImageToPhotosAlbum({
    filePath: filePath,
    success: () => {
      wx.showToast({
        title: '保存成功',
        icon: 'success'
      });
    },
    fail: (err) => {
      console.error('保存到相册失败:', err);
      if (err.errMsg.includes('auth')) {
        wx.showModal({
          title: '权限不足',
          content: '请在设置中允许访问相册权限',
          confirmText: '去设置',
          success: (res) => {
            if (res.confirm) {
              wx.openSetting();
            }
          }
        });
      } else {
        wx.showModal({
          title: '保存失败',
          content: '图片保存失败，请重试',
          showCancel: false
        });
      }
    }
  });
}
```

#### 4. 图案加载错误

```javascript
// 图案管理器错误处理
class PatternManager {
  create(patternKey, params) {
    try {
      const PatternClass = this.patterns.get(patternKey);
      if (!PatternClass) {
        console.warn(`图案 ${patternKey} 不存在，使用默认图案`);
        return this.create('zigzag', params);
      }
      return new PatternClass(params);
    } catch (error) {
      console.error('创建图案失败:', error);
      // 返回空图案，避免应用崩溃
      return new EmptyPattern();
    }
  }
}
```

## 测试策略

### 单元测试

#### 1. 渲染引擎测试

```javascript
describe('RenderEngine', () => {
  let renderEngine;
  let mockCanvas;
  let mockContext;
  
  beforeEach(() => {
    mockContext = {
      clearRect: jest.fn(),
      fillRect: jest.fn(),
      fillText: jest.fn(),
      // ... 其他Canvas方法
    };
    mockCanvas = { getContext: () => mockContext };
    renderEngine = new RenderEngine(mockCanvas, mockContext);
  });
  
  test('应该正确清空画布', () => {
    renderEngine.clearCanvas();
    expect(mockContext.clearRect).toHaveBeenCalledWith(0, 0, 375, 375);
  });
  
  test('应该正确绘制背景', () => {
    renderEngine.drawBackground('#ffffff');
    expect(mockContext.fillStyle).toBe('#ffffff');
    expect(mockContext.fillRect).toHaveBeenCalledWith(0, 0, 375, 375);
  });
});
```

#### 2. 图案管理器测试

```javascript
describe('PatternManager', () => {
  let patternManager;
  
  beforeEach(() => {
    patternManager = new PatternManager();
  });
  
  test('应该正确加载所有图案', () => {
    const patterns = patternManager.getAllPatternsInfo();
    expect(patterns).toBeInstanceOf(Array);
    expect(patterns.length).toBeGreaterThan(0);
  });
  
  test('应该正确创建图案实例', () => {
    const pattern = patternManager.create('zigzag', { density: 20 });
    expect(pattern).toBeInstanceOf(BasePattern);
  });
});
```

### 集成测试

#### 1. 完整渲染流程测试

```javascript
describe('完整渲染流程', () => {
  test('应该正确渲染包含文字和干扰线的画布', async () => {
    const config = {
      textConfig: {
        content: '测试文字',
        fontSize: 50,
        color: '#000000'
      },
      patternConfig: {
        enabled: true,
        key: 'zigzag',
        params: { density: 20 }
      },
      backgroundColor: '#ffffff'
    };
    
    const result = await renderFullCanvas(config);
    expect(result.success).toBe(true);
  });
});
```

#### 2. 导出功能测试

```javascript
describe('图片导出功能', () => {
  test('应该正确导出PNG格式图片', async () => {
    const exportResult = await exportImage('png', 0.9);
    expect(exportResult.tempFilePath).toBeDefined();
    expect(exportResult.tempFilePath).toMatch(/\.png$/);
  });
});
```

### 性能测试

#### 1. 渲染性能测试

```javascript
describe('渲染性能', () => {
  test('复杂图案渲染应在16ms内完成', () => {
    const startTime = performance.now();
    renderComplexPattern();
    const endTime = performance.now();
    
    expect(endTime - startTime).toBeLessThan(16); // 60fps要求
  });
});
```

#### 2. 内存使用测试

```javascript
describe('内存使用', () => {
  test('长时间使用不应出现内存泄漏', () => {
    const initialMemory = getMemoryUsage();
    
    // 模拟长时间使用
    for (let i = 0; i < 1000; i++) {
      renderCanvas();
      clearCanvas();
    }
    
    const finalMemory = getMemoryUsage();
    expect(finalMemory - initialMemory).toBeLessThan(10 * 1024 * 1024); // 10MB
  });
});
```

### 用户体验测试

#### 1. 响应时间测试

- 参数调整到画布更新：< 100ms
- 图案切换：< 200ms
- 图片导出：< 3s

#### 2. 兼容性测试

- 不同设备屏幕尺寸适配
- 不同像素密度适配
- iOS/Android兼容性

#### 3. 错误恢复测试

- 网络异常恢复
- 权限拒绝处理
- 内存不足处理

## 性能优化

### 渲染优化

1. **批量更新**：避免频繁的Canvas重绘
2. **脏区域检测**：只重绘变化的区域
3. **离屏渲染**：复杂图案预渲染到离屏Canvas
4. **requestAnimationFrame**：使用动画帧同步更新

### 内存优化

1. **对象池**：复用Canvas路径对象
2. **及时清理**：清理不再使用的图案实例
3. **图片压缩**：导出前进行适当压缩

### 用户体验优化

1. **防抖处理**：滑块调整时防抖更新
2. **加载状态**：显示操作进度
3. **错误提示**：友好的错误信息展示