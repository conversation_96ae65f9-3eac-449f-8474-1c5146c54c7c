/**
 * utils/patterns/HoneycombPattern.js
 * 蜂窝图案 - 六边形蜂窝状结构
 */

const BasePattern = require('./BasePattern');

class HoneycombPattern extends BasePattern {

  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      hexSize: 20,           // 六边形大小
      spacing: 30,           // 六边形间距
      strokeWidth: 1,        // 描边宽度
      showStroke: true,      // 是否显示描边
      showFill: true,        // 是否显示填充
      lineColor: '#000000',  // 线条颜色（从全局传入）
      ...options
    });
  }

  /**
   * 获取参数配置
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'hexSize',
        label: '六边形大小',
        type: 'slider',
        min: 10,
        max: 40,
        step: 1,
        default: 20
      },
      {
        key: 'spacing',
        label: '间距',
        type: 'slider',
        min: 25,
        max: 60,
        step: 1,
        default: 30
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 0.5,
        max: 3,
        step: 0.5,
        default: 1
      },
      {
        key: 'showStroke',
        label: '显示描边',
        type: 'switch',
        default: true
      },
      {
        key: 'showFill',
        label: '显示填充',
        type: 'switch',
        default: true
      }
    ];
  }

  /**
   * 绘制六边形（平顶）
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} centerX - 中心X坐标
   * @param {number} centerY - 中心Y坐标
   * @param {number} size - 六边形大小
   */
  drawHexagon(ctx, centerX, centerY, size) {
    ctx.beginPath();

    // 绘制平顶六边形（上下边水平）
    // 从右边开始，顺时针绘制
    for (let i = 0; i < 6; i++) {
      const angle = (Math.PI / 3) * i; // 从0度开始，每60度一个顶点
      const x = centerX + size * Math.cos(angle);
      const y = centerY + size * Math.sin(angle);

      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }

    ctx.closePath();

    // 填充
    if (this.options.showFill) {
      ctx.fillStyle = '#ffffff';
      ctx.fill();
    }

    // 描边
    if (this.options.showStroke) {
      ctx.strokeStyle = this.options.lineColor;
      ctx.lineWidth = this.options.strokeWidth;
      ctx.stroke();
    }
  }

  /**
   * 绘制蜂窝图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展
   */
  draw(ctx, width, height, margin = 30) {
    const { hexSize, spacing } = this.options;

    // 平顶六边形的标准几何尺寸
    const hexRadius = hexSize;
    const hexWidth = hexRadius * Math.sqrt(3);   // 六边形宽度
    const hexHeight = hexRadius * 2;             // 六边形高度

    // 直接使用spacing作为六边形中心之间的距离
    const actualXSpacing = spacing;
    const actualYSpacing = spacing * 0.866; // spacing * sin(60°) 保持蜂窝比例

    const cols = Math.ceil((width + 2 * margin) / actualXSpacing) + 2;
    const rows = Math.ceil((height + 2 * margin) / actualYSpacing) + 2;

    ctx.save();

    // 绘制蜂窝网格
    for (let row = 0; row < rows; row++) {
      for (let col = 0; col < cols; col++) {
        let x = col * actualXSpacing - margin;
        let y = row * actualYSpacing - margin;

        // 奇数行向右偏移半个水平间距（标准蜂窝排列）
        if (row % 2 === 1) {
          x += actualXSpacing * 0.5;
        }

        // 只绘制在画布范围内的六边形
        if (x >= -margin - hexRadius && x <= width + margin + hexRadius &&
          y >= -margin - hexRadius && y <= height + margin + hexRadius) {
          this.drawHexagon(ctx, x, y, hexRadius);
        }
      }
    }

    ctx.restore();
  }

  /**
   * 获取图案信息
   * @returns {Object} 图案信息
   */
  getInfo() {
    return {
      name: '蜂窝',
      description: '六边形蜂窝状干扰线，支持填充和描边',
      category: 'geometric'
    };
  }
}

module.exports = HoneycombPattern;