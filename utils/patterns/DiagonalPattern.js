/**
 * utils/patterns/DiagonalPattern.js
 * 对角线图案
 */

const BasePattern = require('./BasePattern');

/**
 * 对角线图案 - 斜线条纹
 */
class DiagonalPattern extends BasePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      lineSpacing: 20,
      direction: 'vertical', // 'horizontal', 'vertical', 'both'
      enableStroke: false, // 是否开启描边
      enableGapLines: false, // 是否在相邻线之间绘制白线
      strokeWidth: 2, // 描边宽度
      gapWidth: 3, // 间隙宽度
      ...options
    });
  }

  /**
   * 获取对角线图案的参数配置
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'lineSpacing',
        label: '线间距',
        type: 'slider',
        min: 1,
        max: 50,
        step: 1,
        default: 8
      },
      {
        key: 'lineWidth',
        label: '线宽',
        type: 'slider',
        min: 1,
        max: 15,
        step: 1,
        default: 1
      },
      {
        key: 'direction',
        label: '方向',
        type: 'picker',
        options: ['左斜', '右斜', '双向'],
        values: ['horizontal', 'vertical', 'both'],
        default: 'vertical'
      },
      {
        key: 'enableStroke',
        label: '开启描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 2,
        condition: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableGapLines',
        label: '绘制间隙线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableGapLines', value: true }
      },
      {
        key: 'gapWidth',
        label: '间隙宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 3,
        condition: { key: 'enableGapLines', value: true }
      }
    ];
  }

  /**
   * 绘制图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 20) {
    // 设置画布样式
    this.setCanvasStyle(ctx);

    // 获取扩展区域
    const area = this.getExtendedArea(width, height, margin);

    // 绘制左斜对角线
    if (this.options.direction === 'horizontal') {
      this.drawLeftToRightDiagonals(ctx, area.width, area.height, area.x, area.y);
    }

    // 绘制右斜对角线
    else if (this.options.direction === 'vertical') {
      this.drawRightToLeftDiagonals(ctx, area.width, area.height, area.x, area.y);
    }

    else {
      this.drawBothDiagonals(ctx, area.width, area.height, area.x, area.y);
    }

    // 重置画布样式
    this.resetCanvasStyle(ctx);
  }

  /**
   * 绘制从左上到右下的对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawLeftToRightDiagonals(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { lineSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算对角线的总长度和需要的线条数量
    const diagonalLength = Math.sqrt(width * width + height * height);
    const totalLines = Math.ceil(diagonalLength / lineSpacing) * 2; // 确保足够的线条覆盖

    // 计算起始偏移，确保线条均匀分布
    const startOffset = -totalLines * lineSpacing / 2;

    // 是否支持描边
    if (enableStroke) {
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawLeftToRightDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawLeftToRightDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth);
      }
    }

    // 绘制主线
    for (let i = 0; i < totalLines; i++) {
      const d = startOffset + i * lineSpacing;
      this.drawLeftToRightDiagonalDefault(ctx, width, height, offsetX, offsetY, d);
    }
  }

  /**
   * 绘制默认左上到右下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   */
  drawLeftToRightDiagonalDefault(ctx, width, height, offsetX, offsetY, d) {
    ctx.beginPath();

    // 计算对角线的方向向量
    const dx = 1;
    const dy = 1;
    const len = Math.sqrt(dx * dx + dy * dy);
    const unitX = dx / len;
    const unitY = dy / len;

    // 计算垂直于对角线的单位向量
    const perpX = -unitY;
    const perpY = unitX;

    // 计算对角线中心点
    const centerX = width / 2 + offsetX;
    const centerY = height / 2 + offsetY;

    // 计算偏移后的中心点
    const offsetCenterX = centerX + perpX * d;
    const offsetCenterY = centerY + perpY * d;

    // 计算线段的起点和终点
    // 延伸足够长以确保覆盖整个画布
    const extendLength = Math.sqrt(width * width + height * height);
    let startX = offsetCenterX - unitX * extendLength;
    let startY = offsetCenterY - unitY * extendLength;
    let endX = offsetCenterX + unitX * extendLength;
    let endY = offsetCenterY + unitY * extendLength;

    // 裁剪线段以适应画布边界
    // 左边界
    if (startX < offsetX) {
      const t = (offsetX - startX) / (endX - startX);
      startX = offsetX;
      startY = startY + t * (endY - startY);
    } else if (endX < offsetX) {
      const t = (offsetX - startX) / (endX - startX);
      endX = offsetX;
      endY = startY + t * (endY - startY);
    }

    // 上边界
    if (startY < offsetY) {
      const t = (offsetY - startY) / (endY - startY);
      startY = offsetY;
      startX = startX + t * (endX - startX);
    } else if (endY < offsetY) {
      const t = (offsetY - startY) / (endY - startY);
      endY = offsetY;
      endX = startX + t * (endX - startX);
    }

    // 右边界
    if (startX > width + offsetX) {
      const t = (width + offsetX - startX) / (endX - startX);
      startX = width + offsetX;
      startY = startY + t * (endY - startY);
    } else if (endX > width + offsetX) {
      const t = (width + offsetX - startX) / (endX - startX);
      endX = width + offsetX;
      endY = startY + t * (endY - startY);
    }

    // 下边界
    if (startY > height + offsetY) {
      const t = (height + offsetY - startY) / (endY - startY);
      startY = height + offsetY;
      startX = startX + t * (endX - startX);
    } else if (endY > height + offsetY) {
      const t = (height + offsetY - startY) / (endY - startY);
      endY = height + offsetY;
      endX = startX + t * (endX - startX);
    }

    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }

  /**
   * 绘制带描边的左上到右下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   * @param {number} strokeWidth - 描边宽度
   */
  drawLeftToRightDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();

    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;

    // 绘制描边（白色）
    ctx.strokeStyle = '#ffffff';
    this.drawLeftToRightDiagonalDefault(ctx, width, height, offsetX, offsetY, d);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制带间隙的左上到右下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   * @param {number} gapWidth - 间隙宽度
   */
  drawLeftToRightDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = '#ffffff';

    // 计算对角线的方向向量
    const dx = 1;
    const dy = 1;
    const len = Math.sqrt(dx * dx + dy * dy);
    const unitX = dx / len;
    const unitY = dy / len;

    // 计算垂直于对角线的单位向量
    const perpX = -unitY;
    const perpY = unitX;

    // 在主线两侧绘制白线，使用垂直向量计算偏移
    this.drawLeftToRightDiagonalDefault(ctx, width, height, offsetX + perpX * gapWidth, offsetY + perpY * gapWidth, d);
    this.drawLeftToRightDiagonalDefault(ctx, width, height, offsetX - perpX * gapWidth, offsetY - perpY * gapWidth, d);

    ctx.restore();
  }

  /**
   * 绘制从右上到左下的对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawRightToLeftDiagonals(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { lineSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算对角线的总长度和需要的线条数量
    const diagonalLength = Math.sqrt(width * width + height * height);
    const totalLines = Math.ceil(diagonalLength / lineSpacing) * 2; // 确保足够的线条覆盖

    // 计算起始偏移，确保线条均匀分布
    const startOffset = -totalLines * lineSpacing / 2;

    // 是否支持描边
    if (enableStroke) {
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawRightToLeftDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawRightToLeftDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth);
      }
    }

    // 绘制主线
    for (let i = 0; i < totalLines; i++) {
      const d = startOffset + i * lineSpacing;
      this.drawRightToLeftDiagonalDefault(ctx, width, height, offsetX, offsetY, d);
    }
  }

  /**
   * 绘制默认右上到左下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   */
  drawRightToLeftDiagonalDefault(ctx, width, height, offsetX, offsetY, d) {
    ctx.beginPath();

    // 计算对角线的方向向量（右上到左下）
    const dx = -1;
    const dy = 1;
    const len = Math.sqrt(dx * dx + dy * dy);
    const unitX = dx / len;
    const unitY = dy / len;

    // 计算垂直于对角线的单位向量
    const perpX = -unitY;
    const perpY = unitX;

    // 计算对角线中心点
    const centerX = width / 2 + offsetX;
    const centerY = height / 2 + offsetY;

    // 计算偏移后的中心点
    const offsetCenterX = centerX + perpX * d;
    const offsetCenterY = centerY + perpY * d;

    // 计算线段的起点和终点
    // 延伸足够长以确保覆盖整个画布
    const extendLength = Math.sqrt(width * width + height * height);
    let startX = offsetCenterX - unitX * extendLength;
    let startY = offsetCenterY - unitY * extendLength;
    let endX = offsetCenterX + unitX * extendLength;
    let endY = offsetCenterY + unitY * extendLength;

    // 裁剪线段以适应画布边界
    // 左边界
    if (startX < offsetX) {
      const t = (offsetX - startX) / (endX - startX);
      startX = offsetX;
      startY = startY + t * (endY - startY);
    } else if (endX < offsetX) {
      const t = (offsetX - startX) / (endX - startX);
      endX = offsetX;
      endY = startY + t * (endY - startY);
    }

    // 上边界
    if (startY < offsetY) {
      const t = (offsetY - startY) / (endY - startY);
      startY = offsetY;
      startX = startX + t * (endX - startX);
    } else if (endY < offsetY) {
      const t = (offsetY - startY) / (endY - startY);
      endY = offsetY;
      endX = startX + t * (endX - startX);
    }

    // 右边界
    if (startX > width + offsetX) {
      const t = (width + offsetX - startX) / (endX - startX);
      startX = width + offsetX;
      startY = startY + t * (endY - startY);
    } else if (endX > width + offsetX) {
      const t = (width + offsetX - startX) / (endX - startX);
      endX = width + offsetX;
      endY = startY + t * (endY - startY);
    }

    // 下边界
    if (startY > height + offsetY) {
      const t = (height + offsetY - startY) / (endY - startY);
      startY = height + offsetY;
      startX = startX + t * (endX - startX);
    } else if (endY > height + offsetY) {
      const t = (height + offsetY - startY) / (endY - startY);
      endY = height + offsetY;
      endX = startX + t * (endX - startX);
    }

    ctx.moveTo(startX, startY);
    ctx.lineTo(endX, endY);
    ctx.stroke();
  }

  /**
   * 绘制带描边的右上到左下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   * @param {number} strokeWidth - 描边宽度
   */
  drawRightToLeftDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();

    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;

    // 绘制描边（白色）
    ctx.strokeStyle = '#ffffff';
    this.drawRightToLeftDiagonalDefault(ctx, width, height, offsetX, offsetY, d);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制带间隙的右上到左下对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   * @param {number} d - 对角线偏移量
   * @param {number} gapWidth - 间隙宽度
   */
  drawRightToLeftDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = '#ffffff';

    // 计算对角线的方向向量（右上到左下）
    const dx = -1;
    const dy = 1;
    const len = Math.sqrt(dx * dx + dy * dy);
    const unitX = dx / len;
    const unitY = dy / len;

    // 计算垂直于对角线的单位向量
    const perpX = -unitY;
    const perpY = unitX;

    // 在主线两侧绘制白线，使用垂直向量计算偏移
    this.drawRightToLeftDiagonalDefault(ctx, width, height, offsetX + perpX * gapWidth, offsetY + perpY * gapWidth, d);
    this.drawRightToLeftDiagonalDefault(ctx, width, height, offsetX - perpX * gapWidth, offsetY - perpY * gapWidth, d);

    ctx.restore();
  }

  /**
   * 绘制双向对角线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawBothDiagonals(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { lineSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算对角线的总长度和需要的线条数量
    const diagonalLength = Math.sqrt(width * width + height * height);
    const totalLines = Math.ceil(diagonalLength / lineSpacing) * 2; // 确保足够的线条覆盖

    // 计算起始偏移，确保线条均匀分布
    const startOffset = -totalLines * lineSpacing / 2;

    // 是否支持描边
    if (enableStroke) {
      // 左斜对角线描边
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawLeftToRightDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth);
      }
      // 右斜对角线描边
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawRightToLeftDiagonalStroke(ctx, width, height, offsetX, offsetY, d, strokeWidth);
      }
    }

    if (enableGapLines) {
      // 左斜对角线间隙
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawLeftToRightDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth);
      }
      // 右斜对角线间隙
      for (let i = 0; i < totalLines; i++) {
        const d = startOffset + i * lineSpacing;
        this.drawRightToLeftDiagonalGap(ctx, width, height, offsetX, offsetY, d, gapWidth);
      }
    }

    // 绘制主线
    // 左斜对角线
    for (let i = 0; i < totalLines; i++) {
      const d = startOffset + i * lineSpacing;
      this.drawLeftToRightDiagonalDefault(ctx, width, height, offsetX, offsetY, d);
    }
    // 右斜对角线
    for (let i = 0; i < totalLines; i++) {
      const d = startOffset + i * lineSpacing;
      this.drawRightToLeftDiagonalDefault(ctx, width, height, offsetX, offsetY, d);
    }
  }
}

module.exports = DiagonalPattern;