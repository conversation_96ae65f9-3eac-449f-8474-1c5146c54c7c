/**
 * utils/patterns/IInterferencePattern.js
 * 干扰线图案接口定义
 */

/**
 * 干扰线图案接口
 * 所有图案类型必须实现这些方法
 */
class IInterferencePattern {
  /**
   * 绘制图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 0) {
    throw new Error('必须实现 draw 方法');
  }

  /**
   * 更新配置
   * @param {Object} options - 新的配置选项
   */
  updateOptions(options) {
    throw new Error('必须实现 updateOptions 方法');
  }
}

module.exports = IInterferencePattern;