/**
 * utils/patterns/DotPattern.js
 * 点阵图案
 */

const BasePattern = require('./BasePattern');

/**
 * 点阵图案 - 规律的点状分布
 */
class DotPattern extends BasePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      dotSize: 2,
      dotSpacing: 15,
      opacity: 0.4,
      direction: 'vertical', // 'horizontal', 'vertical', 'both'
      enableStroke: false, // 是否开启描边
      enableGapLines: false, // 是否在相邻线之间绘制白线
      strokeWidth: 1, // 描边宽度
      gapWidth: 4, // 间隙宽度
      strokeMargin: 2, // 描边与点边缘的间距
      ...options
    });
  }

  /**
   * 获取点阵图案的参数配置
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'dotSize',
        label: '点大小',
        type: 'slider',
        min: 1,
        max: 20,
        step: 1,
        default: 4
      },
      {
        key: 'dotSpacing',
        label: '点间距',
        type: 'slider',
        min: 5,
        max: 30,
        step: 1,
        default: 15
      },
      {
        key: 'opacity',
        label: '透明度',
        type: 'slider',
        min: 0.1,
        max: 1,
        step: 0.1,
        default: 1
      },
      {
        key: 'direction',
        label: '方向',
        type: 'picker',
        options: ['水平', '垂直', '双向'],
        values: ['horizontal', 'vertical', 'both'],
        default: 'both'
      },
      {
        key: 'enableStroke',
        label: '开启描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 2,
        condition: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableGapLines',
        label: '绘制间隙线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableGapLines', value: true }
      },
      {
        key: 'gapWidth',
        label: '间隙宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 3,
        condition: { key: 'enableGapLines', value: true }
      }
    ];
  }

  /**
   * 绘制图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 20) {
    // 设置画布样式
    this.setCanvasStyle(ctx);
    
    // 获取扩展区域
    const area = this.getExtendedArea(width, height, margin);

    // 根据方向绘制点阵
    if (this.options.direction === 'horizontal') {
      this.drawHorizontalDots(ctx, area.width, area.height, area.x, area.y);
    } else if (this.options.direction === 'vertical') {
      this.drawVerticalDots(ctx, area.width, area.height, area.x, area.y);
    } else {
      this.drawBothDots(ctx, area.width, area.height, area.x, area.y);
    }
    
    // 重置画布样式
    this.resetCanvasStyle(ctx);
  }

  /**
   * 绘制水平方向的点
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawHorizontalDots(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { dotSize, dotSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保点阵对齐
    const startX = Math.floor(offsetX / dotSpacing) * dotSpacing;
    const y = height / 2 + offsetY; // 水平居中

    // 存储所有点的位置
    const dotPositions = [];
    for (let x = startX; x < width + offsetX; x += dotSpacing) {
      dotPositions.push({ x, y });
    }

    // 是否支持描边
    if (enableStroke) {
      this.drawAllStrokes(ctx, dotPositions, dotSize, strokeWidth);
    }

    if (enableGapLines) {
      this.drawAllGaps(ctx, dotPositions, dotSize, gapWidth);
    }

    // 绘制主点
    this.drawAllDots(ctx, dotPositions, dotSize);
  }

  /**
   * 绘制垂直方向的点
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawVerticalDots(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { dotSize, dotSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保点阵对齐
    const startY = Math.floor(offsetY / dotSpacing) * dotSpacing;
    const x = width / 2 + offsetX; // 垂直居中

    // 存储所有点的位置
    const dotPositions = [];
    for (let y = startY; y < height + offsetY; y += dotSpacing) {
      dotPositions.push({ x, y });
    }

    // 是否支持描边
    if (enableStroke) {
      this.drawAllStrokes(ctx, dotPositions, dotSize, strokeWidth);
    }

    if (enableGapLines) {
      this.drawAllGaps(ctx, dotPositions, dotSize, gapWidth);
    }

    // 绘制主点
    this.drawAllDots(ctx, dotPositions, dotSize);
  }

  /**
   * 绘制双向点阵
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawBothDots(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { dotSize, dotSpacing, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保点阵对齐
    const startX = Math.floor(offsetX / dotSpacing) * dotSpacing;
    const startY = Math.floor(offsetY / dotSpacing) * dotSpacing;

    // 存储所有点的位置
    const dotPositions = [];
    for (let x = startX; x < width + offsetX; x += dotSpacing) {
      for (let y = startY; y < height + offsetY; y += dotSpacing) {
        dotPositions.push({ x, y });
      }
    }

    // 是否支持描边
    if (enableStroke) {
      this.drawAllStrokes(ctx, dotPositions, dotSize, strokeWidth);
    }

    if (enableGapLines) {
      this.drawAllGaps(ctx, dotPositions, dotSize, gapWidth);
    }

    // 绘制主点
    this.drawAllDots(ctx, dotPositions, dotSize);
  }



  /**
   * 批量绘制所有点
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} positions - 点位置数组，每个元素包含x和y坐标
   * @param {number} size - 点大小
   */
  drawAllDots(ctx, positions, size) {
    ctx.save();
    ctx.fillStyle = this.options.lineColor;
    ctx.globalAlpha = this.options.opacity;

    // 一次性绘制所有点，确保坐标对齐到像素边界
    for (const pos of positions) {
      const x = Math.round(pos.x) + 0.5;
      const y = Math.round(pos.y) + 0.5;
      ctx.beginPath();
      ctx.arc(x, y, size, 0, 2 * Math.PI);
      ctx.fill();
    }

    ctx.restore();
  }

  /**
   * 批量绘制所有描边
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} positions - 点位置数组，每个元素包含x和y坐标
   * @param {number} size - 点大小
   * @param {number} strokeWidth - 描边宽度
   */
  drawAllStrokes(ctx, positions, size, strokeWidth) {
    const strokeMargin = this.options.strokeMargin || 0;

    // 先绘制所有白色描边，确保坐标对齐到像素边界
    ctx.save();
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = this.options.opacity;

    for (const pos of positions) {
      const x = Math.round(pos.x) + 0.5;
      const y = Math.round(pos.y) + 0.5;
      ctx.beginPath();
      ctx.arc(x, y, size + strokeMargin + strokeWidth, 0, 2 * Math.PI);
      ctx.fill();
    }

    ctx.restore();
  }

  /**
   * 批量绘制所有间隙点
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {Array} positions - 点位置数组，每个元素包含x和y坐标
   * @param {number} size - 点大小
   * @param {number} gapWidth - 间隙宽度
   */
  drawAllGaps(ctx, positions, size, gapWidth) {
    const strokeMargin = this.options.strokeMargin || size;
    const actualGap = size + strokeMargin + gapWidth;

    // 绘制所有白色间隙点
    ctx.save();
    ctx.fillStyle = '#ffffff';
    ctx.globalAlpha = this.options.opacity * 0.8;

    for (const pos of positions) {
      // 上方小点
      ctx.beginPath();
      ctx.arc(pos.x, pos.y - actualGap, size * 0.6, 0, 2 * Math.PI);
      ctx.fill();

      // 右侧小点
      ctx.beginPath();
      ctx.arc(pos.x + actualGap, pos.y, size * 0.6, 0, 2 * Math.PI);
      ctx.fill();

      // 下方小点
      ctx.beginPath();
      ctx.arc(pos.x, pos.y + actualGap, size * 0.6, 0, 2 * Math.PI);
      ctx.fill();

      // 左侧小点
      ctx.beginPath();
      ctx.arc(pos.x - actualGap, pos.y, size * 0.6, 0, 2 * Math.PI);
      ctx.fill();
    }

    ctx.restore();
  }
}

module.exports = DotPattern;