/**
 * utils/patterns/GridPattern.js
 * 网格图案
 */

const BasePattern = require('./BasePattern');

/**
 * 网格图案 - 规整的网格线条
 */
class GridPattern extends BasePattern {
    /**
     * 构造函数
     * @param {Object} options - 配置选项
     */
    constructor(options = {}) {
        super({
            gridSize: 20,
            opacity: 1.0,
            direction: 'vertical', // 'horizontal', 'vertical', 'both'
            enableStroke: false, // 是否开启描边
            enableGapLines: false, // 是否在相邻线之间绘制白线
            strokeWidth: 2, // 描边宽度
            gapWidth: 3, // 间隙宽度
            ...options
        });
    }

    /**
     * 绘制图案
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     * @param {number} margin - 边距扩展，用于确保图案铺满画布
     */
    draw(ctx, width, height, margin = 20) {
        // 设置画布样式
        this.setCanvasStyle(ctx);

        // 获取扩展区域
        const area = this.getExtendedArea(width, height, margin);

        // 绘制垂直线
        if (this.options.direction === 'vertical') {
            this.drawVerticalLines(ctx, area.width, area.height, area.x, area.y);
        }

        // 绘制水平线
        else if (this.options.direction === 'horizontal') {
            this.drawHorizontalLines(ctx, area.width, area.height, area.x, area.y);
        } 

        else {
            this.drawBothLines(ctx, area.width, area.height, area.x, area.y);
        }

        // 重置画布样式
        this.resetCanvasStyle(ctx);
    }

    /**
     * 绘制垂直线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     * @param {number} offsetX - X轴偏移
     * @param {number} offsetY - Y轴偏移
     */
    drawVerticalLines(ctx, width, height, offsetX = 0, offsetY = 0) {
        const { gridSize, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

        // 计算起始位置，确保网格对齐
        const startX = Math.floor(offsetX / gridSize) * gridSize;

        //是否支持描边
        if (enableStroke) {
            for (let x = startX; x < width + offsetX; x += gridSize) {
                this.drawVerticalStrokeLine(ctx, x, offsetY, height, strokeWidth);
            }

        }

        if (enableGapLines) {
            for (let x = startX; x < width + offsetX; x += gridSize) {
                this.drawVerticalGapLine(ctx, x, offsetY, height, gapWidth);
            }
        }


        //绘制主线
        for (let x = startX; x < width + offsetX; x += gridSize) {
            this.drawVerticalDefaultLine(ctx, x, offsetY, height);
        }
    }

    /**
     * 绘制默认垂直线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} x - 线的X坐标
     * @param {number} offsetY - Y轴偏移
     * @param {number} height - 线的高度
     */
    drawVerticalDefaultLine(ctx, x, offsetY, height) {
        ctx.beginPath();
        ctx.moveTo(x, offsetY);
        ctx.lineTo(x, height + offsetY);
        ctx.stroke();
    }

    /**
     * 绘制带描边的垂直线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} x - 线的X坐标
     * @param {number} offsetY - Y轴偏移
     * @param {number} height - 线的高度
     * @param {number} strokeWidth - 描边宽度
     */
    drawVerticalStrokeLine(ctx, x, offsetY, height, strokeWidth) {
        // 保存当前上下文状态
        ctx.save();

        // 设置线宽
        const originalLineWidth = ctx.lineWidth;
        ctx.lineWidth = originalLineWidth + strokeWidth * 2;

        // 绘制描边（白色）
        ctx.strokeStyle = '#ffffff';
        this.drawVerticalDefaultLine(ctx, x, offsetY, height);

        // 恢复线宽
        ctx.lineWidth = originalLineWidth;
        // 绘制主线（原色）
        ctx.strokeStyle = this.options.lineColor;
        // 恢复上下文状态
        ctx.restore();
    }

    /**
     * 绘制带间隙的垂直线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} x - 线的X坐标
     * @param {number} offsetY - Y轴偏移
     * @param {number} height - 线的高度
     * @param {number} gapWidth - 间隙宽度
     */
    drawVerticalGapLine(ctx, x, offsetY, height, gapWidth) {
        // 绘制白线（间隙）
        ctx.save();
        ctx.strokeStyle = '#ffffff';
        const gx = x + (this.options.gridSize) / 2
        // 设置线宽
        const originalLineWidth = ctx.lineWidth;
        ctx.lineWidth = gapWidth
        // 在主线两侧绘制白线
        this.drawVerticalDefaultLine(ctx, gx, offsetY, height);

        // 恢复线宽
        ctx.lineWidth = originalLineWidth;
        // 绘制主线（原色）
        ctx.strokeStyle = this.options.lineColor;
        ctx.restore();
    }

    /**
     * 绘制水平线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     * @param {number} offsetX - X轴偏移
     * @param {number} offsetY - Y轴偏移
     */
    drawHorizontalLines(ctx, width, height, offsetX = 0, offsetY = 0) {
        const { gridSize, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

        // 计算起始位置，确保网格对齐
        const startY = Math.floor(offsetY / gridSize) * gridSize;

        // 根据不同的选项绘制
        if (enableStroke) {
            for (let y = startY; y < height + offsetY; y += gridSize) {
                // 描边模式
                this.drawHorizontalStrokeLine(ctx, offsetX, y, width, strokeWidth);
            }
        }

        if (enableGapLines) {
            for (let y = startY; y < height + offsetY; y += gridSize) {
                // 间隙模式
                this.drawHorizontalGapLine(ctx, offsetX, y, width, gapWidth);
            }
        }

        for (let y = startY; y < height + offsetY; y += gridSize) {

            // 默认模式
            this.drawHorizontalDefaultLine(ctx, offsetX, y, width);

        }
    }

    /**
     * 绘制默认水平线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} offsetX - X轴偏移
     * @param {number} y - 线的Y坐标
     * @param {number} width - 线的宽度
     */
    drawHorizontalDefaultLine(ctx, offsetX, y, width) {
        ctx.beginPath();
        ctx.moveTo(offsetX, y);
        ctx.lineTo(width + offsetX, y);
        ctx.stroke();
    }

    /**
     * 绘制带描边的水平线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} offsetX - X轴偏移
     * @param {number} y - 线的Y坐标
     * @param {number} width - 线的宽度
     * @param {number} strokeWidth - 描边宽度
     */
    drawHorizontalStrokeLine(ctx, offsetX, y, width, strokeWidth) {
        // 保存当前上下文状态
        ctx.save();

        // 设置线宽
        const originalLineWidth = ctx.lineWidth;
        ctx.lineWidth = originalLineWidth + strokeWidth * 2;

        // 绘制描边（白色）
        ctx.strokeStyle = '#ffffff';
        this.drawHorizontalDefaultLine(ctx, offsetX, y, width);

        // 恢复线宽
        ctx.lineWidth = originalLineWidth;
        // 绘制主线（原色）
        ctx.strokeStyle = this.options.lineColor;
        // 恢复上下文状态
        ctx.restore();
    }

    /**
     * 绘制带间隙的水平线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} offsetX - X轴偏移
     * @param {number} y - 线的Y坐标
     * @param {number} width - 线的宽度
     * @param {number} gapWidth - 间隙宽度
     */
    drawHorizontalGapLine(ctx, offsetX, y, width, gapWidth) {
        // 绘制主线（原色）
        this.drawHorizontalDefaultLine(ctx, offsetX, y, width);

        // 绘制白线（间隙）
        ctx.save();
        ctx.strokeStyle = '#ffffff';
        // 设置线宽
        const originalLineWidth = ctx.lineWidth;
        ctx.lineWidth  = gapWidth;

        // 在主线两侧绘制白线
        const gy = y + (this.options.gridSize) / 2
        this.drawHorizontalDefaultLine(ctx, offsetX, gy, width);

        // 恢复线宽
        ctx.lineWidth = originalLineWidth;
        // 绘制主线（原色）
        ctx.strokeStyle = this.options.lineColor;
        ctx.restore();
    }

    /**
     * 绘制双向线
     * @param {CanvasRenderingContext2D} ctx - 画布上下文
     * @param {number} width - 画布宽度
     * @param {number} height - 画布高度
     * @param {number} offsetX - X轴偏移
     * @param {number} offsetY - Y轴偏移
     */
    drawBothLines(ctx, width, height, offsetX = 0, offsetY = 0) {
        const { gridSize, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

        // 计算起始位置，确保网格对齐
        const startY = Math.floor(offsetY / gridSize) * gridSize;
        // 计算起始位置，确保网格对齐
        const startX = Math.floor(offsetX / gridSize) * gridSize;

        //是否支持描边
        if (enableStroke) {
            for (let x = startX; x < width + offsetX; x += gridSize) {
                this.drawVerticalStrokeLine(ctx, x, offsetY, height, strokeWidth);
            }

            for (let y = startY; y < height + offsetY; y += gridSize) {
                // 描边模式
                this.drawHorizontalStrokeLine(ctx, offsetX, y, width, strokeWidth);
            }

        }

        if (enableGapLines) {
            for (let x = startX; x < width + offsetX; x += gridSize) {
                this.drawVerticalGapLine(ctx, x, offsetY, height, gapWidth);
            }
            for (let y = startY; y < height + offsetY; y += gridSize) {
                // 间隙模式
                this.drawHorizontalGapLine(ctx, offsetX, y, width, gapWidth);
            }
        }


        //绘制主线
        for (let x = startX; x < width + offsetX; x += gridSize) {
            this.drawVerticalDefaultLine(ctx, x, offsetY, height);
        }

        for (let y = startY; y < height + offsetY; y += gridSize) {
            // 默认模式
            this.drawHorizontalDefaultLine(ctx, offsetX, y, width);

        }
    }
}

/**
 * 获取网格图案的参数配置
 * @returns {Array} 参数配置数组
 */

GridPattern.getParameterConfig = function () {
    return [
        {
            key: 'gridSize',
            label: '网格大小',
            type: 'slider',
            min: 1,
            max: 20,
            step: 1,
            default: 8
        },
        {
            key: 'lineWidth',
            label: '线宽',
            type: 'slider',
            min: 1,
            max: 5,
            step: 1,
            default: 1
        },
        {
            key: 'direction',
            label: '方向',
            type: 'picker',
            options: ['水平', '垂直', '双向'],
            values: ['horizontal', 'vertical', 'both'],
            default: 'horizontal'
        },
        {
          key: 'enableStroke',
          label: '开启描边',
          type: 'switch',
          default: true
        },
        {
          key: 'strokeColor',
          label: '描边颜色',
          type: 'color',
          default: '#FFFFFF',
          showIf: { key: 'enableStroke', value: true }
        },
        {
          key: 'strokeWidth',
          label: '描边宽度',
          type: 'slider',
          min: 1,
          max: 10,
          step: 1,
          default: 2,
          condition: { key: 'enableStroke', value: true }
        },
        {
          key: 'enableGapLines',
          label: '绘制间隙线',
          type: 'switch',
          default: false
        },
        {
          key: 'midlineColor',
          label: '中间线颜色',
          type: 'color',
          default: '#FFFFFF',
          showIf: { key: 'enableGapLines', value: true }
        },
        {
          key: 'gapWidth',
          label: '间隙宽度',
          type: 'slider',
          min: 1,
          max: 10,
          step: 1,
          default: 3,
          condition: { key: 'enableGapLines', value: true }
        }
    ];
}

module.exports = GridPattern;