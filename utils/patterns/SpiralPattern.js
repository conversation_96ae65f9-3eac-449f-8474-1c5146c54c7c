const BasePattern = require('./BasePattern');

class SpiralPattern extends BasePattern {
  constructor(options = {}) {
    super({
      lineWidth: 1,
      lineColor: '#cccccc',
      spacing: 5, // 螺旋线间距
      turns: 5, // 螺旋圈数
      centerX: 0.5, // 圆心X比例
      centerY: 0.5, // 圆心Y比例
      enableStroke: false, // 是否启用描边
      strokeWidth: 2, // 描边宽度
      strokeColor: '#FFFFFF', // 描边颜色，默认白色
      enableMidline: false, // 是否启用中间线
      midlineWidth: 1, // 中间线宽度
      midlineColor: '#FFFFFF' // 中间线颜色，默认白色
    });
    this.updateOptions(options);
  }

  draw(ctx, width, height, margin = 0) {
    this.setCanvasStyle(ctx);
    const { lineWidth, lineColor, spacing, turns, centerX, centerY, enableStroke, strokeWidth, strokeColor, enableMidline, midlineWidth, midlineColor } = this.options;

    const extendedArea = this.getExtendedArea(width, height, margin);
    const actualCenterX = extendedArea.x + extendedArea.width * centerX;
    const actualCenterY = extendedArea.y + extendedArea.height * centerY;

    const maxRadius = Math.max(extendedArea.width, extendedArea.height) / 2;
    const totalLength = turns * 2 * Math.PI;

    // 绘制描边螺旋线
    if (enableStroke) {
      ctx.beginPath();
      ctx.strokeStyle = strokeColor;
      ctx.lineWidth = strokeWidth;
      for (let i = 0; i <= totalLength; i += 0.1) {
        const radius = maxRadius * (i / totalLength);
        const x = actualCenterX + radius * Math.cos(i);
        const y = actualCenterY + radius * Math.sin(i);
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.stroke();
    }

    // 绘制主螺旋线
    ctx.beginPath();
    ctx.strokeStyle = lineColor;
    ctx.lineWidth = lineWidth;
    for (let i = 0; i <= totalLength; i += 0.1) {
      const radius = maxRadius * (i / totalLength);
      const x = actualCenterX + radius * Math.cos(i);
      const y = actualCenterY + radius * Math.sin(i);
      if (i === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    }
    ctx.stroke();

    // 绘制中间线螺旋线
    if (enableMidline) {
      ctx.beginPath();
      ctx.strokeStyle = midlineColor;
      ctx.lineWidth = midlineWidth;
      for (let i = 0; i <= totalLength; i += 0.1) {
        const radius = maxRadius * (i / totalLength) + spacing / 2;
        const x = actualCenterX + radius * Math.cos(i);
        const y = actualCenterY + radius * Math.sin(i);
        if (i === 0) {
          ctx.moveTo(x, y);
        } else {
          ctx.lineTo(x, y);
        }
      }
      ctx.stroke();
    }

    this.resetCanvasStyle(ctx);
  }

  static getParameterConfig() {
    return [
      ...super.getParameterConfig(), // 继承BasePattern的参数
      {
        key: 'centerX',
        label: '圆心X位置',
        type: 'slider',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5
      },
      {
        key: 'centerY',
        label: '圆心Y位置',
        type: 'slider',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5
      },
      {
        key: 'spacing',
        label: '螺旋间距',
        type: 'slider',
        min: 1,
        max: 20,
        step: 1,
        default: 5
      },
      {
        key: 'turns',
        label: '螺旋圈数',
        type: 'slider',
        min: 10,
        max: 60,
        step: 1,
        default: 30
      },
      {
        key: 'enableStroke',
        label: '启用描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 20,
        step: 1,
        default: 6,
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableMidline',
        label: '启用中间线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineWidth',
        label: '中间线宽度',
        type: 'slider',
        min: 1,
        max: 5,
        step: 1,
        default: 1,
        showIf: { key: 'enableMidline', value: true }
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableMidline', value: true }
      }
    ];
  }
}

module.exports = SpiralPattern;