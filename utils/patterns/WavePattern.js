/**
 * utils/patterns/WavePattern.js
 * 波浪线图案
 */

const BasePattern = require('./BasePattern');

/**
 * 波浪线图案 - 平滑的正弦波形
 */
class WavePattern extends BasePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      amplitude: 10,
      frequency: 0.02,
      direction: 'vertical',
      enableStroke: false, // 是否开启描边
      enableGapLines: false, // 是否在相邻线之间绘制白线
      strokeWidth: 2, // 描边宽度
      gapWidth: 3, // 间隙宽度
      ...options
    });
  }

  /**
   * 绘制图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 20) {
    // 设置画布样式
    this.setCanvasStyle(ctx);
    
    // 获取扩展区域
    const area = this.getExtendedArea(width, height, margin);

    // 绘制水平波浪线
    if (this.options.direction === 'horizontal') {
      this.drawHorizontalWaves(ctx, area.width, area.height, area.x, area.y);
    }

    // 绘制垂直波浪线
    else if (this.options.direction === 'vertical') {
      this.drawVerticalWaves(ctx, area.width, area.height, area.x, area.y);
    }

    else {
      this.drawBothWaves(ctx, area.width, area.height, area.x, area.y);
    }
    
    // 重置画布样式
    this.resetCanvasStyle(ctx);
  }

  /**
   * 绘制水平波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawHorizontalWaves(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, amplitude, frequency, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;
    
    // 计算起始位置，确保波浪对齐
    const startY = Math.floor(offsetY / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalWaveStroke(ctx, width, offsetX, y, amplitude, frequency, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalWaveGap(ctx, width, offsetX, y, amplitude, frequency, gapWidth);
      }
    }

    // 绘制主线
    for (let y = startY; y < height + offsetY; y += spacing) {
      this.drawHorizontalWaveDefault(ctx, width, offsetX, y, amplitude, frequency);
    }
  }
  
  /**
   * 绘制默认水平波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   */
  drawHorizontalWaveDefault(ctx, width, offsetX, y, amplitude, frequency) {
    ctx.beginPath();
    ctx.moveTo(offsetX, y);
    
    for (let x = offsetX; x <= width + offsetX; x++) {
      const waveY = y + Math.sin((x - offsetX) * frequency) * amplitude;
      ctx.lineTo(x, waveY);
    }
    ctx.stroke();
  }
  
  /**
   * 绘制带描边的水平波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   * @param {number} strokeWidth - 描边宽度
   */
  drawHorizontalWaveStroke(ctx, width, offsetX, y, amplitude, frequency, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();
    
    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;
    
    // 绘制描边（白色）
    ctx.strokeStyle = '#ffffff';
    this.drawHorizontalWaveDefault(ctx, width, offsetX, y, amplitude, frequency);
    
    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }
  
  /**
   * 绘制带间隙的水平波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   * @param {number} gapWidth - 间隙宽度
   */
  drawHorizontalWaveGap(ctx, width, offsetX, y, amplitude, frequency, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = '#ffffff';
    
    // 在主线上方绘制白线
    this.drawHorizontalWaveDefault(ctx, width, offsetX, y - gapWidth, amplitude, frequency);
    
    // 在主线下方绘制白线
    this.drawHorizontalWaveDefault(ctx, width, offsetX, y + gapWidth, amplitude, frequency);
    
    ctx.restore();
  }

  /**
   * 绘制垂直波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawVerticalWaves(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, amplitude, frequency, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;
    
    // 计算起始位置，确保波浪对齐
    const startX = Math.floor(offsetX / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalWaveStroke(ctx, height, x, offsetY, amplitude, frequency, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalWaveGap(ctx, height, x, offsetY, amplitude, frequency, gapWidth);
      }
    }

    // 绘制主线
    for (let x = startX; x < width + offsetX; x += spacing) {
      this.drawVerticalWaveDefault(ctx, height, x, offsetY, amplitude, frequency);
    }
  }
  
  /**
   * 绘制默认垂直波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   */
  drawVerticalWaveDefault(ctx, height, x, offsetY, amplitude, frequency) {
    ctx.beginPath();
    ctx.moveTo(x, offsetY);
    
    for (let y = offsetY; y <= height + offsetY; y++) {
      const waveX = x + Math.sin((y - offsetY) * frequency) * amplitude;
      ctx.lineTo(waveX, y);
    }
    ctx.stroke();
  }
  
  /**
   * 绘制带描边的垂直波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   * @param {number} strokeWidth - 描边宽度
   */
  drawVerticalWaveStroke(ctx, height, x, offsetY, amplitude, frequency, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();
    
    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;
    
    // 绘制描边（白色）
    ctx.strokeStyle = '#ffffff';
    this.drawVerticalWaveDefault(ctx, height, x, offsetY, amplitude, frequency);
    
    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }
  
  /**
   * 绘制带间隙的垂直波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} amplitude - 波浪振幅
   * @param {number} frequency - 波浪频率
   * @param {number} gapWidth - 间隙宽度
   */
  drawVerticalWaveGap(ctx, height, x, offsetY, amplitude, frequency, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = '#ffffff';
    
    // 在主线左侧绘制白线
    this.drawVerticalWaveDefault(ctx, height, x - gapWidth, offsetY, amplitude, frequency);
    
    // 在主线右侧绘制白线
    this.drawVerticalWaveDefault(ctx, height, x + gapWidth, offsetY, amplitude, frequency);
    
    ctx.restore();
  }

  /**
   * 绘制双向波浪线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawBothWaves(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, amplitude, frequency, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;
    
    // 计算起始位置，确保波浪对齐
    const startY = Math.floor(offsetY / spacing) * spacing;
    const startX = Math.floor(offsetX / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      // 水平波浪线描边
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalWaveStroke(ctx, width, offsetX, y, amplitude, frequency, strokeWidth);
      }
      // 垂直波浪线描边
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalWaveStroke(ctx, height, x, offsetY, amplitude, frequency, strokeWidth);
      }
    }

    if (enableGapLines) {
      // 水平波浪线间隙
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalWaveGap(ctx, width, offsetX, y, amplitude, frequency, gapWidth);
      }
      // 垂直波浪线间隙
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalWaveGap(ctx, height, x, offsetY, amplitude, frequency, gapWidth);
      }
    }

    // 绘制主线
    // 水平波浪线
    for (let y = startY; y < height + offsetY; y += spacing) {
      this.drawHorizontalWaveDefault(ctx, width, offsetX, y, amplitude, frequency);
    }
    // 垂直波浪线
    for (let x = startX; x < width + offsetX; x += spacing) {
      this.drawVerticalWaveDefault(ctx, height, x, offsetY, amplitude, frequency);
    }
  }

  /**
   * 获取波浪线图案的参数配置
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'amplitude',
        label: '波浪振幅',
        type: 'slider',
        min: 5,
        max: 25,
        step: 1,
        default: 10
      },
      {
        key: 'frequency',
        label: '波浪频率',
        type: 'slider',
        min: 0.01,
        max: 0.1,
        step: 0.001,
        default: 0.02
      },
      {
        key: 'lineWidth',
        label: '线宽',
        type: 'slider',
        min: 1,
        max: 5,
        step: 1,
        default: 1
      },
      {
        key: 'spacing',
        label: '线间隔',
        type: 'slider',
        min: 3,
        max: 50,
        step: 1,
        default: 15
      },
      {
        key: 'direction',
        label: '方向',
        type: 'picker',
        options: ['水平', '垂直', '双向'],
        values: ['horizontal', 'vertical', 'both'],
        default: 'both'
      },
      {
        key: 'enableStroke',
        label: '开启描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 2,
        condition: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableGapLines',
        label: '绘制间隙线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableGapLines', value: true }
      },
      {
        key: 'gapWidth',
        label: '间隙宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 3,
        condition: { key: 'enableGapLines', value: true }
      }
    ];
  }
}

module.exports = WavePattern;