/**
 * utils/patterns/ZigzagPattern.js
 * 锯齿线图案
 */

const BasePattern = require('./BasePattern');

/**
 * 锯齿线图案 - 规律的锯齿形状
 */
class ZigzagPattern extends BasePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      zigzagLength: 15,
      zigzagHeight: 8,
      direction: 'vertical', // 'horizontal', 'vertical', 'both'
      enableStroke: true, // 是否开启描边
      enableGapLines: false, // 是否在相邻线之间绘制白线
      strokeWidth: 2, // 描边宽度
      strokeColor: '#FFFFFF', // 描边颜色，默认白色
      gapWidth: 3, // 间隙宽度
      midlineColor: '#FFFFFF', // 中间线颜色，默认白色
      ...options
    });
  }

  /**
   * 绘制图案
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 20) {
    // 设置画布样式
    this.setCanvasStyle(ctx);

    // 获取扩展区域
    const area = this.getExtendedArea(width, height, margin);

    // 绘制水平锯齿线
    if (this.options.direction === 'horizontal') {
      this.drawHorizontalZigzag(ctx, area.width, area.height, area.x, area.y);
    }

    // 绘制垂直锯齿线
    else if (this.options.direction === 'vertical') {
      this.drawVerticalZigzag(ctx, area.width, area.height, area.x, area.y);
    }

    else {
      this.drawBothZigzag(ctx, area.width, area.height, area.x, area.y);
    }

    // 重置画布样式
    this.resetCanvasStyle(ctx);
  }

  /**
   * 绘制水平锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawHorizontalZigzag(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, zigzagLength, zigzagHeight, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保锯齿对齐
    const startY = Math.floor(offsetY / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalZigzagStroke(ctx, width, offsetX, y, zigzagLength, zigzagHeight, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalZigzagGap(ctx, width, offsetX, y, zigzagLength, zigzagHeight, gapWidth);
      }
    }

    // 绘制主线
    for (let y = startY; y < height + offsetY; y += spacing) {
      this.drawHorizontalZigzagDefault(ctx, width, offsetX, y, zigzagLength, zigzagHeight);
    }
  }

  /**
   * 绘制默认水平锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   */
  drawHorizontalZigzagDefault(ctx, width, offsetX, y, zigzagLength, zigzagHeight) {
    ctx.beginPath();
    let x = offsetX;
    let isUp = true; // 控制锯齿方向，保持规律
    ctx.moveTo(x, y);

    while (x < width + offsetX) {
      x += zigzagLength;
      const nextY = y + (isUp ? -zigzagHeight : zigzagHeight);
      ctx.lineTo(Math.min(x, width + offsetX), nextY);
      x += zigzagLength;
      ctx.lineTo(Math.min(x, width + offsetX), y);
      isUp = !isUp; // 交替锯齿方向，保持规律
    }
    ctx.stroke();
  }

  /**
   * 绘制带描边的水平锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   * @param {number} strokeWidth - 描边宽度
   */
  drawHorizontalZigzagStroke(ctx, width, offsetX, y, zigzagLength, zigzagHeight, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();

    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;

    // 绘制描边（白色）
    ctx.strokeStyle = this.options.strokeColor || '#ffffff';
    this.drawHorizontalZigzagDefault(ctx, width, offsetX, y, zigzagLength, zigzagHeight);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制带间隙的水平锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} offsetX - X轴偏移
   * @param {number} y - 线的Y坐标
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   * @param {number} gapWidth - 间隙宽度
   */
  drawHorizontalZigzagGap(ctx, width, offsetX, y, zigzagLength, zigzagHeight, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = this.options.midlineColor || '#ffffff';

    const zy = y + (this.options.spacing) / 2
    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = gapWidth

    // 在主线上方绘制白线
    this.drawHorizontalZigzagDefault(ctx, width, offsetX, zy, zigzagLength, zigzagHeight);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;

    ctx.restore();
  }

  /**
   * 绘制垂直锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawVerticalZigzag(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, zigzagLength, zigzagHeight, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保锯齿对齐
    const startX = Math.floor(offsetX / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalZigzagStroke(ctx, height, x, offsetY, zigzagLength, zigzagHeight, strokeWidth);
      }
    }

    if (enableGapLines) {
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalZigzagGap(ctx, height, x, offsetY, zigzagLength, zigzagHeight, gapWidth);
      }
    }

    // 绘制主线
    for (let x = startX; x < width + offsetX; x += spacing) {
      this.drawVerticalZigzagDefault(ctx, height, x, offsetY, zigzagLength, zigzagHeight);
    }
  }

  /**
   * 绘制默认垂直锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   */
  drawVerticalZigzagDefault(ctx, height, x, offsetY, zigzagLength, zigzagHeight) {
    ctx.beginPath();
    let y = offsetY;
    let isLeft = true; // 控制锯齿方向，保持规律
    ctx.moveTo(x, y);

    while (y < height + offsetY) {
      y += zigzagLength;
      const nextX = x + (isLeft ? -zigzagHeight : zigzagHeight);
      ctx.lineTo(nextX, Math.min(y, height + offsetY));
      y += zigzagLength;
      ctx.lineTo(x, Math.min(y, height + offsetY));
      isLeft = !isLeft; // 交替锯齿方向，保持规律
    }
    ctx.stroke();
  }

  /**
   * 绘制带描边的垂直锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   * @param {number} strokeWidth - 描边宽度
   */
  drawVerticalZigzagStroke(ctx, height, x, offsetY, zigzagLength, zigzagHeight, strokeWidth) {
    // 保存当前上下文状态
    ctx.save();

    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = originalLineWidth + strokeWidth * 2;

    // 绘制描边（白色）
    ctx.strokeStyle = this.options.strokeColor || '#ffffff';
    this.drawVerticalZigzagDefault(ctx, height, x, offsetY, zigzagLength, zigzagHeight);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;
    // 恢复上下文状态
    ctx.restore();
  }

  /**
   * 绘制带间隙的垂直锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} height - 画布高度
   * @param {number} x - 线的X坐标
   * @param {number} offsetY - Y轴偏移
   * @param {number} zigzagLength - 锯齿长度
   * @param {number} zigzagHeight - 锯齿高度
   * @param {number} gapWidth - 间隙宽度
   */
  drawVerticalZigzagGap(ctx, height, x, offsetY, zigzagLength, zigzagHeight, gapWidth) {
    // 绘制白线（间隙）
    ctx.save();
    ctx.strokeStyle = this.options.midlineColor || '#ffffff';

    const zx = x + (this.options.spacing) / 2
    // 设置线宽
    const originalLineWidth = ctx.lineWidth;
    ctx.lineWidth = gapWidth

    // 在主线左侧绘制白线
    this.drawVerticalZigzagDefault(ctx, height, zx, offsetY, zigzagLength, zigzagHeight);

    // 恢复线宽
    ctx.lineWidth = originalLineWidth;
    // 绘制主线（原色）
    ctx.strokeStyle = this.options.lineColor;

    ctx.restore();
  }

  /**
   * 绘制双向锯齿线
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} offsetX - X轴偏移
   * @param {number} offsetY - Y轴偏移
   */
  drawBothZigzag(ctx, width, height, offsetX = 0, offsetY = 0) {
    const { spacing, zigzagLength, zigzagHeight, enableStroke, enableGapLines, strokeWidth, gapWidth } = this.options;

    // 计算起始位置，确保锯齿对齐
    const startY = Math.floor(offsetY / spacing) * spacing;
    const startX = Math.floor(offsetX / spacing) * spacing;

    // 是否支持描边
    if (enableStroke) {
      // 水平锯齿线描边
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalZigzagStroke(ctx, width, offsetX, y, zigzagLength, zigzagHeight, strokeWidth);
      }
      // 垂直锯齿线描边
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalZigzagStroke(ctx, height, x, offsetY, zigzagLength, zigzagHeight, strokeWidth);
      }
    }

    if (enableGapLines) {
      // 水平锯齿线间隙
      for (let y = startY; y < height + offsetY; y += spacing) {
        this.drawHorizontalZigzagGap(ctx, width, offsetX, y, zigzagLength, zigzagHeight, gapWidth);
      }
      // 垂直锯齿线间隙
      for (let x = startX; x < width + offsetX; x += spacing) {
        this.drawVerticalZigzagGap(ctx, height, x, offsetY, zigzagLength, zigzagHeight, gapWidth);
      }
    }

    // 绘制主线
    // 水平锯齿线
    for (let y = startY; y < height + offsetY; y += spacing) {
      this.drawHorizontalZigzagDefault(ctx, width, offsetX, y, zigzagLength, zigzagHeight);
    }
    // 垂直锯齿线
    for (let x = startX; x < width + offsetX; x += spacing) {
      this.drawVerticalZigzagDefault(ctx, height, x, offsetY, zigzagLength, zigzagHeight);
    }
  }

  /**
   * 获取锯齿线图案的参数配置
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'zigzagLength',
        label: '锯齿长度',
        type: 'slider',
        min: 5,
        max: 30,
        step: 1,
        default: 13
      },
      {
        key: 'zigzagHeight',
        label: '锯齿高度',
        type: 'slider',
        min: 2,
        max: 30,
        step: 1,
        default: 8
      },
      {
        key: 'lineWidth',
        label: '线宽',
        type: 'slider',
        min: 1,
        max: 5,
        step: 1,
        default: 1
      },
      {
        key: 'spacing',
        label: '线间隔',
        type: 'slider',
        min: 3,
        max: 30,
        step: 1,
        default: 8
      },
      {
        key: 'direction',
        label: '方向',
        type: 'picker',
        options: ['水平', '垂直', '双向'],
        values: ['horizontal', 'vertical', 'both'],
        default: 'vertical'
      },
      {
        key: 'enableStroke',
        label: '开启描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 2,
        condition: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableGapLines',
        label: '绘制间隙线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableGapLines', value: true }
      },
      {
        key: 'gapWidth',
        label: '间隙宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 3,
        condition: { key: 'enableGapLines', value: true }
      }
    ];
  }
}

module.exports = ZigzagPattern;