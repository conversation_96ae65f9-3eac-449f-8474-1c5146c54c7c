const BasePattern = require('./BasePattern');

class ConcentricCirclePattern extends BasePattern {
  constructor(options = {}) {
    super({
      lineWidth: 1,
      lineColor: '#cccccc',
      spacing: 2, // 圆环间距
      centerX: 0.5, // 圆心X比例
      centerY: 0.5, // 圆心Y比例
      startRadius: 10, // 起始半径
      enableStroke: false, // 是否启用描边
      strokeWidth: 2, // 描边宽度
      strokeColor: '#FFFFFF', // 描边颜色
      enableMidline: false, // 是否启用中间线
      midlineWidth: 1, // 中间线宽度
      midlineColor: '#FFFFFF' // 中间线颜色
    });
    this.updateOptions(options);
  }

  draw(ctx, width, height, margin = 0) {
    this.setCanvasStyle(ctx);
    const { lineWidth, lineColor, spacing, centerX, centerY, startRadius, enableStroke, strokeWidth, strokeColor, enableMidline, midlineWidth, midlineColor } = this.options;

    const extendedArea = this.getExtendedArea(width, height, margin);
    const actualCenterX = extendedArea.x + extendedArea.width * centerX;
    const actualCenterY = extendedArea.y + extendedArea.height * centerY;

    let radius = startRadius;
    while (radius < Math.max(extendedArea.width, extendedArea.height) / 2 + startRadius) {
      // 绘制描边
      if (enableStroke) {
        ctx.beginPath();
        ctx.strokeStyle = strokeColor;
        ctx.lineWidth = strokeWidth;
        ctx.arc(actualCenterX, actualCenterY, radius, 0, 2 * Math.PI);
        ctx.stroke();
      }

      // 绘制主圆环
      ctx.beginPath();
      ctx.strokeStyle = lineColor;
      ctx.lineWidth = lineWidth;
      ctx.arc(actualCenterX, actualCenterY, radius, 0, 2 * Math.PI);
      ctx.stroke();

      // 绘制中间线
      if (enableMidline) {
        ctx.beginPath();
        ctx.strokeStyle = midlineColor;
        ctx.lineWidth = midlineWidth;
        ctx.arc(actualCenterX, actualCenterY, radius + spacing / 2, 0, 2 * Math.PI);
        ctx.stroke();
      }
      radius += spacing;
    }
    this.resetCanvasStyle(ctx);
  }

  static getParameterConfig() {
    return [
      ...super.getParameterConfig(), // 继承BasePattern的参数
      {
        key: 'centerX',
        label: '圆心X位置',
        type: 'slider',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5
      },
      {
        key: 'centerY',
        label: '圆心Y位置',
        type: 'slider',
        min: 0,
        max: 1,
        step: 0.01,
        default: 0.5
      },
      {
        key: 'startRadius',
        label: '起始半径',
        type: 'slider',
        min: 0,
        max: 50,
        step: 1,
        default: 10
      },
      {
        key: 'enableStroke',
        label: '启用描边',
        type: 'switch',
        default: true
      },
      {
        key: 'strokeWidth',
        label: '描边宽度',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 6,
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'strokeColor',
        label: '描边颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableStroke', value: true }
      },
      {
        key: 'enableMidline',
        label: '启用中间线',
        type: 'switch',
        default: false
      },
      {
        key: 'midlineWidth',
        label: '中间线宽度',
        type: 'slider',
        min: 1,
        max: 5,
        step: 1,
        default: 1,
        showIf: { key: 'enableMidline', value: true }
      },
      {
        key: 'midlineColor',
        label: '中间线颜色',
        type: 'color',
        default: '#FFFFFF',
        showIf: { key: 'enableMidline', value: true }
      }
    ];
  }
}

module.exports = ConcentricCirclePattern;