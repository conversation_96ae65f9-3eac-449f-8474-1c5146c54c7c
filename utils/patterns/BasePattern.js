/**
 * utils/patterns/BasePattern.js
 * 干扰线图案基类
 */

const IInterferencePattern = require('./IInterferencePattern');

/**
 * 干扰线图案基类
 * 提供所有图案类型的通用功能
 */
class BasePattern extends IInterferencePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super();
    this.options = {
      lineWidth: 2,
      lineColor: '#cccccc',
      spacing: 25,
      ...options
    };
  }

  /**
   * 绘制图案 - 子类需要实现此方法
   * @param {CanvasRenderingContext2D} ctx - 画布上下文 (Canvas 2D API)
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   * @param {number} margin - 边距扩展，用于确保图案铺满画布
   */
  draw(ctx, width, height, margin = 0) {
    console.log('绘制图案', { ctx, width, height, margin });
    throw new Error('子类必须实现 draw 方法');
  }

  /**
   * 获取扩展的绘制区域
   * @param {number} width - 原始宽度
   * @param {number} height - 原始高度
   * @param {number} margin - 边距扩展
   * @returns {Object} 扩展后的区域 {x, y, width, height}
   */
  getExtendedArea(width, height, margin = 20) {
    return {
      x: -margin,
      y: -margin,
      width: width + margin * 2,
      height: height + margin * 2
    };
  }

  /**
   * 更新配置
   * @param {Object} options - 新的配置选项
   */
  updateOptions(options) {
    this.options = { ...this.options, ...options };
  }

  /**
   * 设置画布样式
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  setCanvasStyle(ctx) {
    const { lineWidth, lineColor, opacity = 1 } = this.options;

    ctx.strokeStyle = lineColor;
    ctx.lineWidth = lineWidth;

    if (opacity !== 1) {
      ctx.globalAlpha = opacity;
    }
  }

  /**
   * 重置画布样式
   * @param {CanvasRenderingContext2D} ctx - 画布上下文
   */
  resetCanvasStyle(ctx) {
    ctx.globalAlpha = 1;
  }

  /**
   * 获取图案的参数配置 - 子类需要实现此方法
   * @returns {Array} 参数配置数组
   */
  static getParameterConfig() {
    return [
      {
        key: 'lineWidth',
        label: '线宽',
        type: 'slider',
        min: 1,
        max: 10,
        step: 1,
        default: 1
      },
      {
        key: 'spacing',
        label: '间距',
        type: 'slider',
        min: 2,
        max: 20,
        step: 1,
        default: 8
      }
    ];
  }
}

module.exports = BasePattern;