/**
 * utils/interferencePatterns.js
 * 干扰线图案系统统一入口
 * 为了向后兼容，提供统一的导出
 */

const PatternManager = require('./PatternManager');
const BasePattern = require('./patterns/BasePattern');
const ZigzagPattern = require('./patterns/ZigzagPattern');
const ConcentricCirclePattern = require('./patterns/ConcentricCirclePattern');
const SpiralPattern = require('./patterns/SpiralPattern');
const HoneycombPattern = require('./patterns/HoneycombPattern');

// 暂时只使用锯齿线图案，其他图案将在后续开发中添加
// const WavePattern = require('./patterns/WavePattern');
// const GridPattern = require('./patterns/GridPattern');
// const DotPattern = require('./patterns/DotPattern');
// const DiagonalPattern = require('./patterns/DiagonalPattern');

// 导出类 - 暂时只导出锯齿线相关类
module.exports = {
  PatternManager,
  BasePattern,
  ZigzagPattern,
  ConcentricCirclePattern,
  SpiralPattern,
  HoneycombPattern
  
  // 其他图案将在后续开发中添加
  // WavePattern,
  // GridPattern,
  // DotPattern,
  // DiagonalPattern
};