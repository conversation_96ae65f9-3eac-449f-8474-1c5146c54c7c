/**
 * utils/PatternManager.js
 * 干扰线图案管理器
 */

const ZigzagPattern = require('./patterns/ZigzagPattern');
const WavePattern = require('./patterns/WavePattern');
const GridPattern = require('./patterns/GridPattern');
const DotPattern = require('./patterns/DotPattern');
const DiagonalPattern = require('./patterns/DiagonalPattern');
const ConcentricCirclePattern = require('./patterns/ConcentricCirclePattern');
const SpiralPattern = require('./patterns/SpiralPattern');
const HoneycombPattern = require('./patterns/HoneycombPattern');

/**
 * 干扰线图案管理器
 * 负责注册、创建和管理所有图案类型
 */
class PatternManager {
  /**
   * 构造函数
   */
  constructor() {
    this.patterns = new Map();
    this.registerDefaultPatterns();
  }

  /**
   * 注册默认图案
   */
  registerDefaultPatterns() {
    this.register('zigzag', ZigzagPattern);
    this.register('wave', WavePattern);
    this.register('grid', GridPattern);
    this.register('dot', DotPattern);
    this.register('diagonal', DiagonalPattern);
    this.register('concentricCircle', ConcentricCirclePattern);
    this.register('spiral', SpiralPattern);
    // this.register('honeycomb', HoneycombPattern);
  }

  /**
   * 注册新图案
   * @param {string} name - 图案名称
   * @param {Class} PatternClass - 图案类
   */
  register(name, PatternClass) {
    this.patterns.set(name, PatternClass);
  }

  /**
   * 创建图案实例
   * @param {string} name - 图案名称
   * @param {Object} options - 配置选项
   * @returns {BasePattern} 图案实例
   */
  create(name, options = {}) {
    const PatternClass = this.patterns.get(name);
    if (!PatternClass) {
      throw new Error(`未找到图案: ${name}`);
    }
    return new PatternClass(options);
  }

  /**
   * 获取所有可用图案名称
   * @returns {Array} 图案名称数组
   */
  getAvailablePatterns() {
    return Array.from(this.patterns.keys());
  }

  /**
   * 获取图案的参数配置
   * @param {string} name - 图案名称
   * @returns {Array} 参数配置数组
   */
  getPatternConfig(name) {
    const PatternClass = this.patterns.get(name);
    if (!PatternClass) {
      throw new Error(`未找到图案: ${name}`);
    }
    return PatternClass.getParameterConfig ? PatternClass.getParameterConfig() : [];
  }

  /**
   * 获取所有图案的信息
   * @returns {Array} 图案信息数组
   */
  getAllPatternsInfo() {
    const patternInfo = {
      'zigzag': {
        name: '锯齿线',
        description: '经典的锯齿状干扰线，适合隐藏文字'
      },
      'wave': {
        name: '波浪线',
        description: '流畅的波浪形干扰线，视觉效果柔和'
      },
      'grid': {
        name: '网格',
        description: '规整的网格状干扰线，覆盖均匀'
      },
      'dot': {
        name: '点阵',
        description: '密集的点状干扰，轻量级遮挡效果'
      },
      'diagonal': {
        name: '对角线',
        description: '斜向的直线干扰，简洁有效'
      },
      'concentricCircle': {
        name: '同心圆',
        description: '圆形的同心圆干扰线，独特的视觉效果'
      },
      'spiral': {
        name: '螺旋',
        description: '螺旋状的干扰线，动感十足'
      },
      // 'honeycomb': {
      //   name: '蜂窝',
      //   description: '六边形蜂窝状干扰线，支持填充和描边'
      // }
    };

    return Array.from(this.patterns.keys()).map(key => ({
      key,
      name: patternInfo[key]?.name || key,
      description: patternInfo[key]?.description || '干扰线图案',
      config: this.getPatternConfig(key)
    }));
  }
}

module.exports = PatternManager;