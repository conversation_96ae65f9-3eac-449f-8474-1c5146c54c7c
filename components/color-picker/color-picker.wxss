.color-picker {
  width: 100%;
}

.color-selector {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.color-preview {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  border: 2rpx solid #e0e0e0;
  cursor: pointer;
}

.color-input {
  font-size: 28rpx;
  color: #333;
  font-family: monospace;
  border: 1rpx solid #e0e0e0;
  border-radius: 6rpx;
  padding: 8rpx 12rpx;
  background: #fff;
  min-width: 120rpx;
  text-align: center;
  transition: border-color 0.2s ease;
}

.color-input:focus {
  border-color: #4CAF50;
  outline: none;
}

.color-picker-panel {
  margin-top: 20rpx;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  border: 1rpx solid #e0e0e0;
}

.preset-colors {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
  margin-bottom: 20rpx;
}

.preset-color {
  width: 40rpx;
  height: 40rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
  cursor: pointer;
}

.preset-color:active {
  transform: scale(0.95);
}

.color-sliders {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.slider-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.slider-label {
  font-size: 28rpx;
  color: #666;
  width: 80rpx;
  flex-shrink: 0;
}

.slider-item slider {
  flex: 1;
}