# Color Picker Component

A reusable color picker component for WeChat Mini Programs.

## Features

- Color preview with hex value display
- **Manual color input** - Type hex values directly (e.g., #FF0000, #f00)
- Preset color palette with 30 predefined colors
- HSL sliders for fine-tuning (Hue, Saturation, Lightness)
- Toggle show/hide functionality
- Custom event handling
- Support for dynamic pattern parameters
- Auto-close other pickers when one opens
- Input validation and formatting

## Usage

### 1. Register the component

In your page's `.json` file:

```json
{
  "usingComponents": {
    "color-picker": "../../components/color-picker/color-picker"
  }
}
```

### 2. Use in WXML

```xml
<view class="form-item">
  <text class="label">颜色选择</text>
  <color-picker 
    color="{{selectedColor}}" 
    preset-colors="{{presetColors}}"
    bind:colorchange="onColorChange"
    bind:toggle="onColorPickerToggle"
    data-type="myColor"
  />
</view>
```

### 3. Handle events in JS

```javascript
Page({
  data: {
    selectedColor: '#ff0000',
    presetColors: [
      '#000000', '#ffffff', '#ff0000', '#00ff00', '#0000ff'
      // ... more colors
    ]
  },

  onColorChange(e) {
    this.setData({
      selectedColor: e.detail.color
    });
    // Do something with the new color
  },

  onColorPickerToggle(e) {
    const type = e.currentTarget.dataset.type;
    const show = e.detail.show;
    
    // Close other color pickers if needed
    if (show) {
      // Logic to close other pickers
    }
  }
})
```

## Properties

| Property | Type | Default | Description |
|----------|------|---------|-------------|
| color | String | '#000000' | Current selected color in hex format |
| label | String | '颜色' | Label for the color picker |
| presetColors | Array | Default palette | Array of preset colors in hex format |

## Events

| Event | Description | Detail |
|-------|-------------|--------|
| colorchange | Triggered when color changes | `{ color: '#ffffff' }` |
| toggle | Triggered when picker is toggled | `{ show: true/false }` |

## Methods

| Method | Description |
|--------|-------------|
| closePicker() | Programmatically close the color picker |

## Example: Multiple Color Pickers

When using multiple color pickers on the same page, use the toggle event to ensure only one is open at a time:

```javascript
onColorPickerToggle(e) {
  const type = e.currentTarget.dataset.type;
  const show = e.detail.show;
  
  if (show) {
    // Close other color pickers
    const query = this.createSelectorQuery();
    query.selectAll('color-picker').exec((res) => {
      if (res && res[0]) {
        res[0].forEach((component, index) => {
          const componentTypes = ['text', 'background', 'line'];
          const componentType = componentTypes[index];
          if (componentType !== type) {
            component.closePicker();
          }
        });
      }
    });
  }
}
```
## 
Dynamic Pattern Parameters

The color picker component supports dynamic pattern parameters. When used in pattern configuration, it automatically handles color parameter changes:

```xml
<!-- In dynamic parameter form -->
<view wx:elif="{{item.type === 'color'}}" class="form-row">
  <view class="form-item">
    <text class="label">{{item.label}}</text>
    <color-picker 
      color="{{patternParams[item.key]}}" 
      preset-colors="{{presetColors}}"
      bind:colorchange="onPatternColorChange"
      bind:toggle="onColorPickerToggle"
      data-key="{{item.key}}"
      data-type="pattern-{{item.key}}"
    />
  </view>
</view>
```

### Pattern Configuration Example

```javascript
// In pattern class getParameterConfig() method
{
  key: 'strokeColor',
  label: '描边颜色',
  type: 'color',
  default: '#FFFFFF'
}
```

### Event Handler

```javascript
onPatternColorChange(e) {
  const key = e.currentTarget.dataset.key;
  const color = e.detail.color;
  
  const updatedParams = {
    ...this.data.patternParams,
    [key]: color
  };

  this.setData({
    patternParams: updatedParams
  });

  this.updateCurrentPattern();
  this.drawCanvas();
}
```
## Co
lor Input Features

### Manual Color Input
Users can directly type hex color values into the input field:
- **Full hex format**: `#FF0000`, `#00FF00`, `#0000FF`
- **Short hex format**: `#F00`, `#0F0`, `#00F` (automatically expanded)
- **Without # prefix**: `FF0000`, `F00` (# is automatically added)
- **Case insensitive**: `#ff0000` or `#FF0000` both work

### Input Validation
- Validates hex color format on blur
- Shows error toast for invalid colors
- Automatically formats and corrects valid inputs
- Reverts to previous color if input is invalid

### Input Behavior
- Real-time display updates while typing
- Color change event only triggered on valid input blur
- Supports both 3-digit and 6-digit hex codes
- Automatically converts to uppercase format

### Example Usage
```xml
<!-- The input field is automatically included in the color picker -->
<color-picker 
  color="{{selectedColor}}" 
  bind:colorchange="onColorChange"
/>
```

The input field will:
1. Display the current color value
2. Allow users to type new hex values
3. Validate input on blur
4. Trigger colorchange event for valid colors
5. Show error message for invalid formats