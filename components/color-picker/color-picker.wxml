<view class="color-picker">
  <view class="color-selector">
    <view class="color-preview" style="background-color: {{color}};" bindtap="onTogglePicker"></view>
    <input class="color-input" type="text" value="{{inputValue}}" placeholder="#000000" bindinput="onColorInput" bindblur="onColorInputBlur" bindfocus="onColorInputFocus" maxlength="7" />
  </view>
  <view class="color-picker-panel" wx:if="{{showPicker}}">
    <view class="preset-colors">
      <view class="preset-color" wx:for="{{presetColors}}" wx:key="*this" style="background-color: {{item}};" bindtap="onSelectPresetColor" data-color="{{item}}"></view>
    </view>
    <view class="color-sliders">
      <view class="slider-item">
        <text class="slider-label">色相</text>
        <slider min="0" max="360" value="{{hsl.h}}" bindchange="onHueChange" activeColor="#4CAF50" />
      </view>
      <view class="slider-item">
        <text class="slider-label">饱和度</text>
        <slider min="0" max="100" value="{{hsl.s}}" bindchange="onSaturationChange" activeColor="#4CAF50" />
      </view>
      <view class="slider-item">
        <text class="slider-label">亮度</text>
        <slider min="0" max="100" value="{{hsl.l}}" bindchange="onLightnessChange" activeColor="#4CAF50" />
      </view>
    </view>
  </view>
</view>