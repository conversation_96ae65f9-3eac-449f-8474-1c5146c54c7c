Component({
  properties: {
    color: {
      type: String,
      value: '#000000'
    },
    label: {
      type: String,
      value: '颜色'
    },
    presetColors: {
      type: Array,
      value: [
        '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
        '#ff0000', '#ff6600', '#ffcc00', '#66ff00', '#00ff66', '#00ffcc',
        '#0066ff', '#6600ff', '#cc00ff', '#ff0066', '#ff3333', '#33ff33',
        '#3333ff', '#ffff33', '#ff33ff', '#33ffff', '#800000', '#008000',
        '#000080', '#808000', '#800080', '#008080', '#c0c0c0', '#808080'
      ]
    }
  },

  data: {
    showPicker: false,
    hsl: { h: 0, s: 0, l: 0 },
    inputValue: ''
  },

  observers: {
    'color': function(newColor) {
      this.setData({
        hsl: this.hexToHsl(newColor),
        inputValue: newColor
      });
    }
  },

  methods: {
    // 颜色转换工具函数
    hexToHsl(hex) {
      const r = parseInt(hex.slice(1, 3), 16) / 255;
      const g = parseInt(hex.slice(3, 5), 16) / 255;
      const b = parseInt(hex.slice(5, 7), 16) / 255;

      const max = Math.max(r, g, b);
      const min = Math.min(r, g, b);
      let h, s, l = (max + min) / 2;

      if (max === min) {
        h = s = 0;
      } else {
        const d = max - min;
        s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
        switch (max) {
          case r: h = (g - b) / d + (g < b ? 6 : 0); break;
          case g: h = (b - r) / d + 2; break;
          case b: h = (r - g) / d + 4; break;
        }
        h /= 6;
      }

      return {
        h: Math.round(h * 360),
        s: Math.round(s * 100),
        l: Math.round(l * 100)
      };
    },

    hslToHex(h, s, l) {
      h /= 360;
      s /= 100;
      l /= 100;

      const hue2rgb = (p, q, t) => {
        if (t < 0) t += 1;
        if (t > 1) t -= 1;
        if (t < 1 / 6) return p + (q - p) * 6 * t;
        if (t < 1 / 2) return q;
        if (t < 2 / 3) return p + (q - p) * (2 / 3 - t) * 6;
        return p;
      };

      let r, g, b;
      if (s === 0) {
        r = g = b = l;
      } else {
        const q = l < 0.5 ? l * (1 + s) : l + s - l * s;
        const p = 2 * l - q;
        r = hue2rgb(p, q, h + 1 / 3);
        g = hue2rgb(p, q, h);
        b = hue2rgb(p, q, h - 1 / 3);
      }

      const toHex = (c) => {
        const hex = Math.round(c * 255).toString(16);
        return hex.length === 1 ? '0' + hex : hex;
      };

      return `#${toHex(r)}${toHex(g)}${toHex(b)}`;
    },

    onTogglePicker() {
      this.setData({
        showPicker: !this.data.showPicker
      });
      
      // 触发外部事件，通知其他颜色选择器关闭
      this.triggerEvent('toggle', { 
        show: !this.data.showPicker 
      });
    },

    onSelectPresetColor(e) {
      const color = e.currentTarget.dataset.color;
      this.setData({
        showPicker: false
      });
      
      this.triggerEvent('colorchange', { 
        color: color 
      });
    },

    onHueChange(e) {
      const hsl = { ...this.data.hsl, h: e.detail.value };
      const hex = this.hslToHex(hsl.h, hsl.s, hsl.l);
      
      this.setData({ hsl });
      this.triggerEvent('colorchange', { 
        color: hex 
      });
    },

    onSaturationChange(e) {
      const hsl = { ...this.data.hsl, s: e.detail.value };
      const hex = this.hslToHex(hsl.h, hsl.s, hsl.l);
      
      this.setData({ hsl });
      this.triggerEvent('colorchange', { 
        color: hex 
      });
    },

    onLightnessChange(e) {
      const hsl = { ...this.data.hsl, l: e.detail.value };
      const hex = this.hslToHex(hsl.h, hsl.s, hsl.l);
      
      this.setData({ hsl });
      this.triggerEvent('colorchange', { 
        color: hex 
      });
    },

    onColorInputFocus(e) {
      // 选中所有文本，方便用户输入新颜色
      // 注意：小程序中input的focus事件不支持直接选中文本
      // 但我们可以在这里做一些其他的处理，比如关闭颜色选择面板
      this.setData({
        showPicker: false
      });
    },

    onColorInput(e) {
      const inputValue = e.detail.value;
      // 实时更新显示，但不触发外部事件
      this.setData({
        inputValue: inputValue
      });
    },

    onColorInputBlur(e) {
      let inputValue = e.detail.value.trim();
      
      // 验证和格式化颜色值
      const validColor = this.validateAndFormatColor(inputValue);
      
      if (validColor) {
        this.setData({
          inputValue: validColor
        });
        
        this.triggerEvent('colorchange', { 
          color: validColor 
        });
      } else {
        // 如果输入无效，恢复到当前颜色
        this.setData({
          inputValue: this.data.color
        });
        
        wx.showToast({
          title: '颜色格式无效',
          icon: 'none',
          duration: 1500
        });
      }
    },

    validateAndFormatColor(input) {
      if (!input) return null;
      
      // 移除空格
      input = input.replace(/\s/g, '');
      
      // 如果没有#前缀，添加它
      if (!input.startsWith('#')) {
        input = '#' + input;
      }
      
      // 验证hex格式
      const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
      if (!hexRegex.test(input)) {
        return null;
      }
      
      // 将3位hex转换为6位
      if (input.length === 4) {
        input = '#' + input[1] + input[1] + input[2] + input[2] + input[3] + input[3];
      }
      
      return input.toUpperCase();
    },

    // 外部调用方法，用于关闭颜色选择器
    closePicker() {
      this.setData({
        showPicker: false
      });
    }
  }
});