/* WeUI 基础样式 - 微信小程序兼容版本 */

page {
  background-color: #f7f7f7;
  font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, "Segoe UI", <PERSON><PERSON>, <PERSON><PERSON>, "PingFang SC", "<PERSON><PERSON>", "Hiragino Sans GB", "Microsoft Yahei", sans-serif;
  font-size: 32rpx;
  line-height: 1.6;
}

/* 基础布局 */
.weui-cells {
  margin-top: 20rpx;
  background-color: #ffffff;
  line-height: 1.41176471;
  font-size: 34rpx;
  overflow: hidden;
  position: relative;
}

.weui-cells:before {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cells:after {
  content: " ";
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  height: 2rpx;
  border-bottom: 2rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 100%;
  transform: scaleY(0.5);
}

.weui-cells_after-title {
  margin-top: 0;
}

.weui-cells__title {
  margin-top: 0.77em;
  margin-bottom: 0.3em;
  padding-left: 30rpx;
  padding-right: 30rpx;
  color: #999999;
  font-size: 28rpx;
}

.weui-cells__tips {
  margin-top: 0.3em;
  color: #999999;
  font-size: 28rpx;
  padding-left: 30rpx;
  padding-right: 30rpx;
}

.weui-cell {
  padding: 20rpx 30rpx;
  position: relative;
  display: flex;
  align-items: center;
}

.weui-cell:before {
  content: " ";
  position: absolute;
  left: 30rpx;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid #e5e5e5;
  color: #e5e5e5;
  transform-origin: 0 0;
  transform: scaleY(0.5);
}

.weui-cell:first-child:before {
  display: none;
}

.weui-cell__hd {
  display: inline-block;
  width: 210rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.weui-cell__bd {
  flex: 1;
}

.weui-cell__ft {
  text-align: right;
  color: #999999;
}

/* 按钮样式 */
.weui-btn {
  position: relative;
  display: block;
  margin-left: auto;
  margin-right: auto;
  padding-left: 28rpx;
  padding-right: 28rpx;
  box-sizing: border-box;
  font-size: 36rpx;
  text-align: center;
  text-decoration: none;
  color: #ffffff;
  line-height: 2.55555556;
  border-radius: 10rpx;
  overflow: hidden;
  background-color: #07c160;
  border: 0;
}

.weui-btn_primary {
  background-color: #07c160;
}

.weui-btn_default {
  color: #353535;
  background-color: #f7f7f7;
}

.weui-btn_warn {
  background-color: #fa5151;
}

.weui-btn_disabled {
  color: rgba(255, 255, 255, 0.6);
  background-color: #c0c4cc;
}

.weui-btn_mini {
  display: inline-block;
  line-height: 2.3;
  font-size: 26rpx;
  padding: 0 1.32em;
}

.weui-btn-area {
  margin: 2.17647059em 30rpx 0.3em;
}

/* 上传组件 */
.weui-uploader {
  flex: 1;
}

.weui-uploader__bd {
  margin-bottom: -8rpx;
  margin-right: -18rpx;
  overflow: hidden;
}

.weui-uploader__files {
  list-style: none;
}

.weui-uploader__file {
  float: left;
  margin-right: 18rpx;
  margin-bottom: 18rpx;
}

.weui-uploader__img {
  display: block;
  width: 158rpx;
  height: 158rpx;
}

.weui-uploader__input-box {
  float: left;
  position: relative;
  margin-right: 18rpx;
  margin-bottom: 18rpx;
  width: 158rpx;
  height: 158rpx;
  border: 2rpx solid #d9d9d9;
  background-color: #f7f7f7;
  box-sizing: border-box;
}

.weui-uploader__input {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
}

/* Flex布局 */
.weui-flex {
  display: flex;
}

.weui-flex-center {
  align-items: center;
}

.weui-flex__item {
  flex: 1;
  text-align: center;
}