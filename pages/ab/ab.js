// AB图生成工具 - 实现Python坦克图算法的微信小程序版本
Page({
  data: {
    imageA: '',       // 正常图片(白色背景显示)
    imageB: '',       // 隐藏图片(黑色背景显示)
    canvasWidth: 0,   // 画布宽度
    canvasHeight: 0,  // 画布高度
    loading: false,   // 加载状态
    generatedImage: false, // 是否已生成AB图
    background: 'white', // 背景颜色
    processingTimeout: null // 处理超时定时器
  },

  // 取消生成
  cancelGeneration() {
    console.log('用户取消生成');
    if (this.data.processingTimeout) {
      clearTimeout(this.data.processingTimeout);
    }
    this.setData({
      loading: false,
      processingTimeout: null
    });
    wx.showToast({
      title: '已取消',
      icon: 'none',
      duration: 1000
    });
  },

  onLoad() {
    // 初始化画布尺寸
    const systemInfo = wx.getSystemInfoSync();
    const canvasWidth = systemInfo.windowWidth - 40; // 减去边距
    const canvasHeight = canvasWidth; // 保持正方形比例
    this.setData({
      canvasWidth: canvasWidth,
      canvasHeight: canvasHeight,
      previewMode: 'normal' // 预览模式: normal, white, black
    });
  },

  // 切换预览模式
  switchPreviewMode() {
    const modes = ['normal', 'white', 'black'];
    const currentIndex = modes.indexOf(this.data.previewMode);
    const nextIndex = (currentIndex + 1) % modes.length;
    const nextMode = modes[nextIndex];

    this.setData({ previewMode: nextMode });

    // 根据预览模式显示不同的背景效果
    const modeNames = {
      'normal': '正常模式',
      'white': '白色背景',
      'black': '黑色背景'
    };

    wx.showToast({
      title: modeNames[nextMode],
      icon: 'none',
      duration: 1000
    });
  },

  // 选择图片A
  chooseImageA() {
    this.chooseImage((path) => {
      this.setData({
        imageA: path,
        generatedImage: false
      });
    });
  },

  // 选择图片B
  chooseImageB() {
    this.chooseImage((path) => {
      this.setData({
        imageB: path,
        generatedImage: false
      });
    });
  },

  // 通用图片选择方法
  chooseImage(callback) {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 使用压缩图片以提高性能
      sourceType: ['album', 'camera'],
      success: (res) => {
        const filePath = res.tempFilePaths[0];

        // 验证文件是否为有效图片
        wx.getImageInfo({
          src: filePath,
          success: (info) => {
            console.log('选择的图片信息:', info);
            callback(filePath);
          },
          fail: (error) => {
            console.error('图片信息获取失败:', error);
            wx.showToast({
              title: '图片格式不支持',
              icon: 'error',
              duration: 2000
            });
          }
        });
      },
      fail: (error) => {
        console.error('选择图片失败:', error);
        if (!error.errMsg.includes('cancel')) {
          wx.showToast({
            title: '选择图片失败',
            icon: 'error',
            duration: 2000
          });
        }
      }
    });
  },

  // 生成AB图(对外暴露的事件处理方法)
  generateABImage() {
    if (!this.validateImages()) {
      return;
    }

    // 设置加载状态
    this.setData({ loading: true });

    // 开始生成AB图
    this.drawCanvas();
  },

  // 绘制AB图(坦克图算法) - 使用Canvas 2D API
  drawCanvas() {
    // 检查是否已选择两张图片
    if (!this.data.imageA || !this.data.imageB) {
      this.setData({ loading: false });
      return;
    }

    console.log('开始生成AB图...');
    console.log('图片A路径:', this.data.imageA);
    console.log('图片B路径:', this.data.imageB);
    console.log('画布尺寸:', this.data.canvasWidth, 'x', this.data.canvasHeight);

    this.setData({ loading: true });

    // 使用Canvas 2D API
    wx.createSelectorQuery()
      .in(this)
      .select('#abCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error('无法获取Canvas 2D节点');
          wx.showToast({ title: 'Canvas不支持', icon: 'error' });
          this.setData({ loading: false });
          return;
        }

        // Canvas 对象
        const canvas = res[0].node;
        // Canvas 画布的实际绘制宽高
        const renderWidth = res[0].width;
        const renderHeight = res[0].height;
        // Canvas 绘制上下文
        const ctx = canvas.getContext('2d');

        console.log('Canvas 2D上下文获取成功');
        console.log('渲染尺寸:', renderWidth, 'x', renderHeight);

        // 设置画布尺寸 - 修复尺寸问题
        const dpr = wx.getSystemInfoSync().pixelRatio;
        console.log('设备像素比:', dpr);

        // 使用固定的画布尺寸，避免缩放问题
        canvas.width = renderWidth;
        canvas.height = renderHeight;

        console.log('设置后的画布尺寸:', canvas.width, 'x', canvas.height);
        console.log('画布尺寸设置完成，开始加载图片...');

        // 开始处理图片
        this.loadImagesAndProcess(ctx, canvas, renderWidth, renderHeight);
      });
  },

  // 加载图片并处理
  loadImagesAndProcess(ctx, canvas, canvasWidth, canvasHeight) {
    console.log('开始加载图片A...');

    // 创建图片A对象
    const imgA = canvas.createImage();
    imgA.onload = () => {
      console.log('图片A加载完成，开始绘制...');
      console.log('图片A尺寸:', imgA.width, 'x', imgA.height);

      // 绘制图片A - 不裁剪，居中显示
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      // 先绘制一个白色背景
      ctx.fillStyle = '#ffffff';
      ctx.fillRect(0, 0, canvasWidth, canvasHeight);

      // 计算图片缩放比例，确保完整显示（不裁剪）
      const scaleX = canvasWidth / imgA.width;
      const scaleY = canvasHeight / imgA.height;
      const scale = Math.min(scaleX, scaleY); // 使用较小的缩放比例确保不裁剪

      const scaledWidth = imgA.width * scale;
      const scaledHeight = imgA.height * scale;
      const offsetX = (canvasWidth - scaledWidth) / 2;
      const offsetY = (canvasHeight - scaledHeight) / 2;

      console.log('图片A缩放信息:', {
        原始尺寸: `${imgA.width}x${imgA.height}`,
        画布尺寸: `${canvasWidth}x${canvasHeight}`,
        缩放比例: scale,
        缩放后尺寸: `${scaledWidth}x${scaledHeight}`,
        偏移: `${offsetX},${offsetY}`
      });

      ctx.drawImage(imgA, offsetX, offsetY, scaledWidth, scaledHeight);

      console.log('图片A绘制完成，获取像素数据...');

      // 获取图片A的像素数据
      const imageDataA = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
      const dataA = imageDataA.data;

      console.log('图片A像素数据获取成功，数据长度:', dataA.length);
      console.log('图片A前20个像素值:', Array.from(dataA.slice(0, 20)));
      console.log('图片A中间区域像素值:', Array.from(dataA.slice(Math.floor(dataA.length / 2), Math.floor(dataA.length / 2) + 20)));
      console.log('开始加载图片B...');

      // 创建图片B对象
      const imgB = canvas.createImage();
      imgB.onload = () => {
        console.log('图片B加载完成，开始绘制...');
        console.log('图片B尺寸:', imgB.width, 'x', imgB.height);

        // 绘制图片B - 不裁剪，居中显示
        ctx.clearRect(0, 0, canvasWidth, canvasHeight);
        // 先绘制一个白色背景
        ctx.fillStyle = '#ffffff';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);

        // 计算图片缩放比例，确保完整显示（不裁剪）
        const scaleX = canvasWidth / imgB.width;
        const scaleY = canvasHeight / imgB.height;
        const scale = Math.min(scaleX, scaleY); // 使用较小的缩放比例确保不裁剪

        const scaledWidth = imgB.width * scale;
        const scaledHeight = imgB.height * scale;
        const offsetX = (canvasWidth - scaledWidth) / 2;
        const offsetY = (canvasHeight - scaledHeight) / 2;

        console.log('图片B缩放信息:', {
          原始尺寸: `${imgB.width}x${imgB.height}`,
          画布尺寸: `${canvasWidth}x${canvasHeight}`,
          缩放比例: scale,
          缩放后尺寸: `${scaledWidth}x${scaledHeight}`,
          偏移: `${offsetX},${offsetY}`
        });

        ctx.drawImage(imgB, offsetX, offsetY, scaledWidth, scaledHeight);

        console.log('图片B绘制完成，获取像素数据...');

        // 获取图片B的像素数据
        const imageDataB = ctx.getImageData(0, 0, canvasWidth, canvasHeight);
        const dataB = imageDataB.data;

        console.log('图片B像素数据获取成功，数据长度:', dataB.length);
        console.log('图片B前20个像素值:', Array.from(dataB.slice(0, 20)));
        console.log('图片B中间区域像素值:', Array.from(dataB.slice(Math.floor(dataB.length / 2), Math.floor(dataB.length / 2) + 20)));
        console.log('开始处理坦克图算法...');

        // 处理坦克图算法
        this.processTankImageCanvas2D(ctx, dataA, dataB, canvasWidth, canvasHeight);
      };

      imgB.onerror = (error) => {
        console.error('图片B加载失败:', error);
        wx.showToast({ title: '图片B加载失败', icon: 'error' });
        this.setData({ loading: false });
      };

      imgB.src = this.data.imageB;
    };

    imgA.onerror = (error) => {
      console.error('图片A加载失败:', error);
      wx.showToast({ title: '图片A加载失败', icon: 'error' });
      this.setData({ loading: false });
    };

    imgA.src = this.data.imageA;
  },

  // 使用Canvas 2D处理坦克图算法
  processTankImageCanvas2D(ctx, dataA, dataB, canvasWidth, canvasHeight) {
    try {
      console.log('开始处理坦克图算法...');
      console.log('数据A长度:', dataA.length, '数据B长度:', dataB.length);
      console.log('画布尺寸:', canvasWidth, 'x', canvasHeight);
      console.log('预期像素数:', canvasWidth * canvasHeight * 4);

      // 创建结果像素数组
      const resultPixels = new Uint8ClampedArray(dataA.length);

      let processedPixels = 0;
      let validAlphaCount = 0;
      let zeroAlphaCount = 0;
      let samplePixels = [];

      // 实现坦克图算法
      for (let i = 0; i < dataA.length; i += 4) {
        // 将RGB转为灰度值
        const pixelA = this.rgbToGray(dataA[i], dataA[i + 1], dataA[i + 2]);
        const pixelB = this.rgbToGray(dataB[i], dataB[i + 1], dataB[i + 2]);

        // 坦克图算法核心公式 - 优化版本
        let alpha = 255 - (pixelA - pixelB);

        // 确保alpha在有效范围内，避免除零和负值
        alpha = Math.max(1, Math.min(255, alpha));

        // 计算灰度值 - 使用更稳定的公式
        let gray = 0;
        if (alpha > 1) {
          gray = Math.floor(255 * pixelB / alpha);
          // 增强对比度
          gray = Math.max(0, Math.min(255, gray));
        } else {
          // 当alpha接近0时，使用pixelB的值
          gray = pixelB;
        }

        // 设置结果像素 (RGBA格式)
        resultPixels[i] = gray;       // R
        resultPixels[i + 1] = gray;   // G  
        resultPixels[i + 2] = gray;   // B
        resultPixels[i + 3] = alpha;  // A

        processedPixels++;
        if (alpha > 1) validAlphaCount++;
        else zeroAlphaCount++;

        // 收集前几个像素的样本数据
        if (processedPixels <= 5) {
          samplePixels.push({
            index: i / 4,
            pixelA, pixelB, alpha, gray,
            originalA: [dataA[i], dataA[i + 1], dataA[i + 2], dataA[i + 3]],
            originalB: [dataB[i], dataB[i + 1], dataB[i + 2], dataB[i + 3]],
            result: [gray, gray, gray, alpha]
          });
        }
      }

      console.log('像素处理完成:');
      console.log('- 处理的像素数:', processedPixels);
      console.log('- 有效alpha像素:', validAlphaCount);
      console.log('- 零alpha像素:', zeroAlphaCount);
      console.log('- 样本像素数据:', samplePixels);

      // 创建ImageData并绘制到画布
      const resultImageData = ctx.createImageData(canvasWidth, canvasHeight);

      // 检查数据长度是否匹配
      if (resultPixels.length !== resultImageData.data.length) {
        console.error('数据长度不匹配:', resultPixels.length, 'vs', resultImageData.data.length);
        throw new Error('数据长度不匹配');
      }

      console.log('设置ImageData...');
      resultImageData.data.set(resultPixels);

      console.log('清空画布并绘制结果...');
      // 清空画布并绘制结果
      ctx.clearRect(0, 0, canvasWidth, canvasHeight);
      ctx.putImageData(resultImageData, 0, 0);

      console.log('AB图生成完成！');

      this.setData({
        loading: false,
        generatedImage: true
      });

      wx.showToast({
        title: 'AB图生成成功',
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      console.error('坦克图处理失败:', error);
      wx.showToast({ title: '处理失败: ' + error.message, icon: 'error' });
      this.setData({ loading: false });
    }
  },

  // 处理坦克图算法
  processTankImage(dataA, dataB, canvasWidth, canvasHeight) {
    try {
      // 创建结果像素数组
      const resultPixels = new Uint8ClampedArray(dataA.length);

      // 实现坦克图算法 - 根据Python代码逻辑
      for (let i = 0; i < dataA.length; i += 4) {
        // 将RGB转为灰度值
        const pixelA = this.rgbToGray(dataA[i], dataA[i + 1], dataA[i + 2]);
        const pixelB = this.rgbToGray(dataB[i], dataB[i + 1], dataB[i + 2]);

        // 坦克图算法核心公式
        const alpha = 255 - (pixelA - pixelB);

        let gray = 0;
        if (alpha > 0) {
          gray = Math.floor(255 * pixelB / alpha);
          gray = Math.max(0, Math.min(255, gray));
        }

        // 设置结果像素 (RGBA格式)
        resultPixels[i] = gray;       // R
        resultPixels[i + 1] = gray;   // G  
        resultPixels[i + 2] = gray;   // B
        resultPixels[i + 3] = Math.max(0, Math.min(255, alpha)); // A
      }

      // 将结果绘制到画布
      this.drawResultToCanvas(resultPixels, canvasWidth, canvasHeight);

    } catch (error) {
      console.error('坦克图处理失败:', error);
      wx.showToast({ title: '处理失败', icon: 'error' });
      this.setData({ loading: false });
    }
  },

  // 将处理后的像素数据绘制到画布
  drawResultToCanvas(resultPixels, canvasWidth, canvasHeight) {
    try {
      // 尝试使用 wx.canvasPutImageData (真机支持)
      wx.canvasPutImageData({
        canvasId: 'abCanvas',
        data: resultPixels,
        x: 0,
        y: 0,
        width: canvasWidth,
        height: canvasHeight,
        success: () => {
          this.setData({
            loading: false,
            generatedImage: true
          });
          wx.showToast({
            title: 'AB图生成成功',
            icon: 'success',
            duration: 2000
          });
        },
        fail: (error) => {
          console.error('canvasPutImageData失败，使用替代方案:', error);
          // 使用替代方案：创建临时画布绘制
          this.drawResultWithAlternativeMethod(resultPixels, canvasWidth, canvasHeight);
        }
      }, this);
    } catch (error) {
      console.error('canvasPutImageData不支持，使用替代方案:', error);
      // 使用替代方案
      this.drawResultWithAlternativeMethod(resultPixels, canvasWidth, canvasHeight);
    }
  },

  // 替代方案：使用简化的混合效果
  drawResultWithAlternativeMethod(resultPixels, canvasWidth, canvasHeight) {
    // 如果像素数据处理不支持，显示一个简化的混合效果作为演示
    this.drawSimpleBlendEffect(canvasWidth, canvasHeight);
  },

  // 简单的混合效果作为演示
  drawSimpleBlendEffect(canvasWidth, canvasHeight) {
    const ctx = wx.createCanvasContext('abCanvas', this);

    // 清空画布
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);

    // 绘制图片A作为底层
    ctx.drawImage(this.data.imageA, 0, 0, canvasWidth, canvasHeight);

    // 设置混合模式并绘制图片B
    ctx.globalAlpha = 0.5; // 设置透明度
    ctx.drawImage(this.data.imageB, 0, 0, canvasWidth, canvasHeight);

    // 重置透明度
    ctx.globalAlpha = 1.0;

    // 添加一个提示文字
    ctx.setFillStyle('rgba(255, 255, 255, 0.8)');
    ctx.fillRect(10, 10, canvasWidth - 20, 60);
    ctx.setFillStyle('#333333');
    ctx.setFontSize(14);
    ctx.fillText('开发者工具预览效果', 20, 30);
    ctx.fillText('请在真机上查看完整AB图效果', 20, 50);

    ctx.draw(false, () => {
      this.setData({
        loading: false,
        generatedImage: true
      });
      wx.showToast({
        title: '预览效果已生成',
        icon: 'success',
        duration: 2000
      });
    });
  },

  // 保存图片到相册 - Canvas 2D版本
  saveImage() {
    if (!this.validateImages()) {
      return;
    }

    wx.showLoading({
      title: '正在保存图片...',
      mask: true
    });

    this.setData({ loading: true });

    // 使用Canvas 2D的保存方法
    wx.createSelectorQuery()
      .in(this)
      .select('#abCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          wx.hideLoading();
          wx.showToast({
            title: '无法获取画布',
            icon: 'error',
            duration: 2000
          });
          this.setData({ loading: false });
          return;
        }

        const canvas = res[0].node;

        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '保存成功',
                  icon: 'success',
                  duration: 2000
                });
              },
              fail: (error) => {
                console.error('保存失败:', error);
                if (error.errMsg.includes('auth')) {
                  wx.showModal({
                    title: '保存失败',
                    content: '请在设置中允许访问相册权限',
                    showCancel: false
                  });
                } else {
                  wx.showToast({
                    title: '保存失败',
                    icon: 'error',
                    duration: 2000
                  });
                }
              },
              complete: () => {
                wx.hideLoading();
                this.setData({ loading: false });
              }
            });
          },
          fail: (error) => {
            console.error('生成图片失败:', error);
            wx.hideLoading();
            wx.showToast({
              title: '生成图片失败',
              icon: 'error',
              duration: 2000
            });
            this.setData({ loading: false });
          }
        }, this);
      });
  },

  // 辅助方法: RGB转灰度
  rgbToGray(r, g, b) {
    return Math.round(0.299 * r + 0.587 * g + 0.114 * b);
  },

  // 辅助方法: 验证图片是否有效
  validateImages() {
    if (!this.data.imageA || !this.data.imageB) {
      wx.showToast({
        title: '请选择两张图片',
        icon: 'none',
        duration: 2000
      });
      return false;
    }
    return true;
  },

  // 设置白色背景
  setWhiteBackground() {
    this.setData({ background: 'white' });
  },

  // 设置黑色背景
  setBlackBackground() {
    this.setData({ background: 'black' });
  },

  // 测试Canvas功能
  testCanvas() {
    console.log('开始测试Canvas功能...');

    wx.createSelectorQuery()
      .in(this)
      .select('#abCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error('无法获取Canvas节点');
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        const renderWidth = res[0].width;
        const renderHeight = res[0].height;

        // 设置画布尺寸
        canvas.width = renderWidth;
        canvas.height = renderHeight;

        // 绘制测试图案
        ctx.clearRect(0, 0, renderWidth, renderHeight);

        // 绘制渐变背景
        const gradient = ctx.createLinearGradient(0, 0, renderWidth, renderHeight);
        gradient.addColorStop(0, 'rgba(255, 0, 0, 0.8)');
        gradient.addColorStop(0.5, 'rgba(0, 255, 0, 0.8)');
        gradient.addColorStop(1, 'rgba(0, 0, 255, 0.8)');

        ctx.fillStyle = gradient;
        ctx.fillRect(0, 0, renderWidth, renderHeight);

        // 添加文字
        ctx.fillStyle = 'white';
        ctx.font = '20px sans-serif';
        ctx.fillText('Canvas测试', 20, 40);
        ctx.fillText('如果看到这个说明Canvas正常', 20, 70);

        console.log('测试图案绘制完成');

        this.setData({
          generatedImage: true
        });
      });
  },

  // 重置所有数据
  resetData() {
    this.setData({
      imageA: '',
      imageB: '',
      loading: false,
      generatedImage: false
    });

    // 清空画布
    wx.createSelectorQuery()
      .in(this)
      .select('#abCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (res[0] && res[0].node) {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          const renderWidth = res[0].width;
          const renderHeight = res[0].height;
          ctx.clearRect(0, 0, renderWidth, renderHeight);
        }
      });
  },

  onShareAppMessage() {
    return {
      title: 'AB图坦克图生成器',
      path: '/pages/ab/ab',
      imageUrl: '' // 可以留空或添加分享图片URL
    };
  }
});