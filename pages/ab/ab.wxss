/* AB图生成工具 - WeUI主题样式 */

/* 页面基础样式 */
.page {
  background-color: #f7f7f7;
  min-height: 100vh;
}

.page__hd {
  padding: 40rpx 30rpx 30rpx;
  text-align: center;
  background: linear-gradient(135deg, #07c160, #4cd964);
  color: white;
}

.page__title {
  font-size: 44rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.page__desc {
  font-size: 28rpx;
  opacity: 0.9;
}

.page__bd {
  padding: 0 30rpx 30rpx;
}

/* WeUI组件自定义样式 */
.weui-cells {
  margin-top: 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.weui-cells__title {
  color: #333333;
  font-weight: 600;
  font-size: 32rpx;
}

.weui-cells__tips {
  color: #999999;
  font-size: 26rpx;
  text-align: center;
  margin-top: 20rpx;
}

.weui-cell {
  padding: 30rpx 20rpx;
}

.weui-label {
  font-size: 32rpx;
  font-weight: 600;
  color: #333333;
  margin-bottom: 8rpx;
}

.weui-label-desc {
  font-size: 24rpx;
  color: #999999;
}

/* 图片上传样式 */
.weui-uploader__file {
  width: 160rpx;
  height: 160rpx;
  border-radius: 12rpx;
  overflow: hidden;
}

.weui-uploader__img {
  width: 100%;
  height: 100%;
}

.weui-uploader__input-box {
  width: 160rpx;
  height: 160rpx;
  border: 2rpx dashed #e5e5e5;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
  position: relative;
}

.weui-uploader__input {
  width: 100%;
  height: 100%;
  position: relative;
}

.weui-uploader__input::before {
  content: '+';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 60rpx;
  color: #999999;
  font-weight: 300;
}

/* 按钮样式 */
.weui-btn {
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.weui-btn_primary {
  background-color: #07c160;
  border-color: #07c160;
}

.weui-btn_mini {
  font-size: 26rpx;
  padding: 0 20rpx;
  height: 60rpx;
  line-height: 60rpx;
}

.weui-btn-area {
  margin-top: 40rpx;
}

/* Flex布局 */
.weui-flex {
  display: flex;
}

.weui-flex__item {
  flex: 1;
  margin: 0 10rpx;
}

.weui-flex__item:first-child {
  margin-left: 0;
}

.weui-flex__item:last-child {
  margin-right: 0;
}

/* 画布容器样式 */
.canvas-container {
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* 创建正方形 */
  position: relative;
  border-radius: 16rpx;
  overflow: hidden;
  border: 2rpx solid #e5e5e5;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.ab-canvas {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: block;
}

/* 加载遮罩样式 */
.weui-mask {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
}

.weui-dialog {
  position: fixed;
  z-index: 5000;
  width: 80%;
  max-width: 600rpx;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background-color: #ffffff;
  text-align: center;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 8rpx 40rpx rgba(0, 0, 0, 0.2);
}

.weui-dialog__bd {
  padding: 60rpx 40rpx 40rpx;
}

.weui-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.weui-loading {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #07c160;
  border-top-color: transparent;
  border-radius: 50%;
  animation: weui-loading 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes weui-loading {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.weui-loading-text {
  font-size: 32rpx;
  color: #333333;
  font-weight: 600;
  margin-bottom: 10rpx;
}

.weui-loading-desc {
  font-size: 26rpx;
  color: #999999;
}

.weui-dialog__ft {
  position: relative;
  line-height: 96rpx;
  font-size: 34rpx;
  display: flex;
  border-top: 1rpx solid #e5e5e5;
}

.weui-dialog__btn {
  display: block;
  flex: 1;
  color: #07c160;
  text-decoration: none;
  background: none;
  border: none;
  font-size: 34rpx;
}

.weui-dialog__btn_default {
  color: #333333;
}

/* 响应式设计 */
@media (max-width: 375px) {
  .page__hd {
    padding: 30rpx 20rpx 20rpx;
  }
  
  .page__title {
    font-size: 40rpx;
  }
  
  .page__bd {
    padding: 0 20rpx 20rpx;
  }
  
  .weui-uploader__file,
  .weui-uploader__input-box {
    width: 140rpx;
    height: 140rpx;
  }
}

/* 动画效果 */
.weui-cells,
.weui-btn,
.canvas-container {
  transition: all 0.3s ease;
}

/* 特殊状态样式 */
.weui-btn_disabled {
  background-color: #c0c4cc !important;
  border-color: #c0c4cc !important;
  color: #909399 !important;
}

.weui-btn_warn {
  background-color: #fa5151;
  border-color: #fa5151;
}

.weui-btn_default {
  background-color: #f7f7f7;
  border-color: #f7f7f7;
  color: #333333;
}