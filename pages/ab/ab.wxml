<!-- AB图生成工具 - WeUI版本 -->
<view class="page">
  <!-- <view class="page__hd">
    <view class="page__title">AB图生成工具</view>
    <view class="page__desc">选择两张图片，生成神奇的AB图效果</view>
  </view> -->

  <view class="page__bd">
    <!-- 图片选择区域 -->
    <view class="weui-cells weui-cells_after-title">
      <view class="weui-cells__title">选择图片</view>
      
      <view class="weui-cell">
        <view class="weui-cell__hd">
          <view class="weui-label">图片A</view>
          <view class="weui-label-desc">白色背景显示</view>
        </view>
        <view class="weui-cell__bd">
          <view class="weui-uploader">
            <view class="weui-uploader__bd">
              <view class="weui-uploader__files">
                <view class="weui-uploader__file" wx:if="{{imageA}}">
                  <image class="weui-uploader__img" src="{{imageA}}" mode="aspectFill"></image>
                </view>
              </view>
              <view class="weui-uploader__input-box" wx:if="{{!imageA}}">
                <view class="weui-uploader__input" bindtap="chooseImageA"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="weui-cell__ft" wx:if="{{imageA}}">
          <button class="weui-btn weui-btn_mini weui-btn_default" bindtap="chooseImageA">重选</button>
        </view>
      </view>

      <view class="weui-cell">
        <view class="weui-cell__hd">
          <view class="weui-label">图片B</view>
          <view class="weui-label-desc">黑色背景显示</view>
        </view>
        <view class="weui-cell__bd">
          <view class="weui-uploader">
            <view class="weui-uploader__bd">
              <view class="weui-uploader__files">
                <view class="weui-uploader__file" wx:if="{{imageB}}">
                  <image class="weui-uploader__img" src="{{imageB}}" mode="aspectFill"></image>
                </view>
              </view>
              <view class="weui-uploader__input-box" wx:if="{{!imageB}}">
                <view class="weui-uploader__input" bindtap="chooseImageB"></view>
              </view>
            </view>
          </view>
        </view>
        <view class="weui-cell__ft" wx:if="{{imageB}}">
          <button class="weui-btn weui-btn_mini weui-btn_default" bindtap="chooseImageB">重选</button>
        </view>
      </view>
    </view>

    <!-- 生成按钮 -->
    <view class="weui-btn-area" wx:if="{{imageA && imageB && !generatedImage}}">
      <button class="weui-btn weui-btn_primary" bindtap="generateABImage" disabled="{{loading}}">
        {{loading ? '正在生成中...' : '生成AB图'}}
      </button>
      <view class="weui-cells__tips">选择完两张图片后，点击按钮开始生成AB图</view>
    </view>

    <!-- 预览区域 -->
    <view class="preview-section">
      <view class="weui-cells weui-cells_after-title">
        <view class="weui-cells__title">预览效果</view>
        
        <!-- 背景切换 -->
        <view class="weui-cell"  wx:if="{{generatedImage}}">
          <view class="weui-cell__bd">
            <view class="weui-flex">
              <view class="weui-flex__item">
                <button class="weui-btn weui-btn_mini {{background === 'white' ? 'weui-btn_primary' : 'weui-btn_default'}}" 
                        bindtap="setWhiteBackground">白色背景</button>
              </view>
              <view class="weui-flex__item">
                <button class="weui-btn weui-btn_mini {{background === 'black' ? 'weui-btn_primary' : 'weui-btn_default'}}" 
                        bindtap="setBlackBackground">黑色背景</button>
              </view>
            </view>
          </view>
        </view>

        <!-- 画布区域 -->
        <view class="weui-cell">
          <view class="weui-cell__bd">
            <view class="canvas-container" style="background-color: {{background}};">
              <canvas type="2d" id="abCanvas" class="ab-canvas"></canvas>
            </view>
          </view>
        </view>
        
        <view class="weui-cells__tips">切换背景颜色可以看到不同的图片效果</view>
      </view>

      <!-- 操作按钮 -->
      <view class="weui-btn-area">
        
        <view class="weui-flex  weui-flex-center" style="margin-top: 20rpx;">
          <view class="weui-flex__item" style="margin-right: 10rpx;">
            <button class="weui-btn weui-btn_default" bindtap="generateABImage" type="default" size="mini">重新生成</button>
          </view>
          <view class="weui-flex__item" style="margin-right: 10rpx;">
            <button class="weui-btn weui-btn_warn" bindtap="resetData" type="error" size="mini">重新选择</button>
          </view>
          <view class="weui-flex__item" style="margin-left: 10rpx;">
            <button class="weui-btn weui-btn_primary" bindtap="saveImage" disabled="{{loading}}" size="mini">保存到相册</button>
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 加载遮罩 -->
  <view class="weui-mask" wx:if="{{loading}}"></view>
  <view class="weui-dialog" wx:if="{{loading}}">
    <view class="weui-dialog__bd">
      <view class="weui-loading-content">
        <view class="weui-loading"></view>
        <view class="weui-loading-text">正在生成AB图...</view>
        <view class="weui-loading-desc">请稍候，这可能需要几秒钟</view>
      </view>
    </view>
    <view class="weui-dialog__ft">
      <button class="weui-dialog__btn weui-dialog__btn_default" bindtap="cancelGeneration">取消</button>
    </view>
  </view>
</view>