/* pages/group_avatar/group_avatar.wxss */
.container {
  display: flex;
  flex-direction: column;
  justify-content: start;
  height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
  width: 100%;
}

.header {
  padding: 20rpx;
  text-align: center;
  margin-bottom: 0;
}

.preview-section {
  padding: 20rpx;
  background-color: #f5f5f5;
  margin-bottom: 0;
  width: 100%;
}

.control-panel {
  flex:1;
}

.edit-section {
  overflow-y: auto;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 16rpx 16rpx 0 0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  box-sizing: border-box;
}





.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 预览区域 */


.canvas-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

.avatar-canvas {
  border-radius: 8rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);
}

/* 编辑区域 */


.section-group {
  margin-bottom: 40rpx;
  box-sizing: border-box;
  width: 100%;
}

.section-group:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

/* 模板选择 */
.template-list {
  white-space: nowrap;
  padding: 10rpx 0;
}

.template-item {
  display: inline-block;
  margin-right: 15rpx;
  border-radius: 8rpx;
  overflow: hidden;
  border: 3rpx solid transparent;
  vertical-align: top;
}

.template-item:last-child {
  margin-right: 0;
}

.template-item.active {
  border-color: #007aff;
}

.template-preview {
  width: 70rpx;
  height: 70rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
}

.template-text {
  font-size: 18rpx;
  color: #fff;
  font-weight: bold;
}

/* 输入组 */
.input-group {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.label {
  width: 140rpx;
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
}

.input {
  flex: 1;
  height: 70rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 26rpx;
  box-sizing: border-box;
  min-width: 0;
}

.input:focus {
  border-color: #007aff;
}

/* 颜色选择 */
.color-group {
  margin-bottom: 25rpx;
}

.color-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.color-list {
  white-space: nowrap;
  padding: 5rpx 0;
}

.color-item {
  display: inline-block;
  width: 45rpx;
  height: 45rpx;
  border-radius: 22rpx;
  margin-right: 12rpx;
  border: 3rpx solid transparent;
  position: relative;
  vertical-align: top;
}

.color-item:last-child {
  margin-right: 0;
}

.color-item.active {
  border-color: #007aff;
}

.color-item.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #fff;
  font-size: 20rpx;
  font-weight: bold;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
}

/* 字体设置 */
.font-group {
  margin-bottom: 25rpx;
  box-sizing: border-box;
  width: 100%;
}

.font-label {
  display: block;
  font-size: 26rpx;
  color: #666;
  margin-bottom: 15rpx;
}

.font-slider {
  width: 100%;
  box-sizing: border-box;
  display: block;
}

/* 装饰设置 */
.decoration-options {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.option-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
}

.option-label {
  font-size: 28rpx;
  color: #333;
}

.option-switch {
  transform: scale(0.8);
}

/* 操作按钮 */
.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 40rpx;
}

.action-btn {
  flex: 1;
  height: 88rpx;
  border: none;
  border-radius: 44rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.save-btn {
  background: linear-gradient(135deg, #00b894, #00a085);
  color: #fff;
}

.share-btn {
  background: linear-gradient(135deg, #fd79a8, #e84393);
  color: #fff;
}