<!--pages/group_avatar/group_avatar.wxml-->
<view class="container">

  <!-- 预览区域 -->
  <view class="preview-section">
    <view class="canvas-container">
      <canvas type="2d" id="avatarCanvas" class="avatar-canvas" style="width: {{canvasSize}}px; height: {{canvasSize}}px;"></canvas>
    </view>
  </view>

  <!-- 控制面板区域 -->
  <scroll-view class="control-panel" scroll-y>
    <!-- 编辑区域 -->
    <view class="edit-section">
      <!-- 模板选择 -->
      <view class="section-group">
        <view class="section-title">选择模板</view>
        <scroll-view class="template-list" scroll-x>
          <view class="template-item {{templateIndex === index ? 'active' : ''}}" 
                wx:for="{{templates}}" wx:key="id" 
                bindtap="selectTemplate" data-index="{{index}}">
            <view class="template-preview" style="background: {{item.background}};">
              <text class="template-text">{{item.name}}</text>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 文字设置 -->
      <view class="section-group">
        <view class="section-title">文字设置</view>
        
        <view class="input-group">
          <text class="label">主标题：</text>
          <input class="input" placeholder="团长" value="{{mainText}}" bindinput="onMainTextChange" maxlength="6" />
        </view>
        
        <view class="input-group">
          <text class="label">副标题：</text>
          <input class="input" placeholder="张三" value="{{subText}}" bindinput="onSubTextChange" maxlength="8" />
        </view>
      </view>

      <!-- 颜色设置 -->
      <view class="section-group">
        <view class="section-title">颜色设置</view>
        
        <view class="color-group">
          <text class="color-label">背景色：</text>
          <scroll-view class="color-list" scroll-x>
            <view class="color-item {{bgColorIndex === index ? 'active' : ''}}"
                  wx:for="{{bgColors}}" wx:key="*this"
                  style="background: {{item}};"
                  bindtap="selectBgColor" data-index="{{index}}">
            </view>
          </scroll-view>
        </view>
        
        <view class="color-group">
          <text class="color-label">文字色：</text>
          <scroll-view class="color-list" scroll-x>
            <view class="color-item {{textColorIndex === index ? 'active' : ''}}"
                  wx:for="{{textColors}}" wx:key="*this"
                  style="background: {{item}};"
                  bindtap="selectTextColor" data-index="{{index}}">
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- 字体设置 -->
      <view class="section-group">
        <view class="section-title">字体设置</view>
        
        <view class="font-group">
          <text class="font-label">主标题大小：</text>
          <slider class="font-slider" min="24" max="300" value="{{mainFontSize}}" 
                  bindchange="onMainFontSizeChange" show-value />
        </view>
        
        <view class="font-group">
          <text class="font-label">副标题大小：</text>
          <slider class="font-slider" min="16" max="72" value="{{subFontSize}}" 
                  bindchange="onSubFontSizeChange" show-value />
        </view>
      </view>

      <!-- 装饰设置 -->
      <view class="section-group">
        <view class="section-title">装饰设置</view>
        
        <view class="decoration-options">
          <view class="option-item">
            <text class="option-label">添加边框</text>
            <switch class="option-switch" checked="{{showBorder}}" bindchange="onBorderChange" />
          </view>
          
          <view class="option-item">
            <text class="option-label">添加阴影</text>
            <switch class="option-switch" checked="{{showShadow}}" bindchange="onShadowChange" />
          </view>
          
          <view class="option-item">
            <text class="option-label">添加图标</text>
            <switch class="option-switch" checked="{{showIcon}}" bindchange="onIconChange" />
          </view>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button class="action-btn save-btn" bindtap="saveAvatar">保存头像</button>
      <button class="action-btn share-btn" bindtap="shareAvatar">分享头像</button>
    </view>
  </scroll-view>
</view>