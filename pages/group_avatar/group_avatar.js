// pages/group_avatar/group_avatar.js
Page({
  data: {
    canvasSize: 300,
    templateIndex: 0,
    mainText: '团长',
    subText: '张三',
    bgColorIndex: 0,
    textColorIndex: 0,
    mainFontSize: 72,
    subFontSize: 36,
    showBorder: true,
    showShadow: true,
    showIcon: false,

    templates: [
      { id: 1, name: '经典', background: 'linear-gradient(135deg, #ff6b6b, #ee5a24)' },
      { id: 2, name: '商务', background: 'linear-gradient(135deg, #3742fa, #2f3542)' },
      { id: 3, name: '清新', background: 'linear-gradient(135deg, #26de81, #20bf6b)' },
      { id: 4, name: '优雅', background: 'linear-gradient(135deg, #a55eea, #8854d0)' },
      { id: 5, name: '活力', background: 'linear-gradient(135deg, #fd79a8, #e84393)' },
      { id: 6, name: '稳重', background: 'linear-gradient(135deg, #636e72, #2d3436)' }
    ],

    bgColors: [
      '#ff6b6b', '#3742fa', '#26de81', '#a55eea',
      '#fd79a8', '#636e72', '#f39c12', '#e74c3c',
      '#9b59b6', '#3498db', '#2ecc71', '#34495e'
    ],

    textColors: [
      '#ffffff', '#000000', '#333333', '#666666',
      '#ffdd59', '#ff6b6b', '#3742fa', '#26de81'
    ]
  },

  onLoad(options) {
    // onLoad时不调用generateAvatar，等待onReady
  },

  onReady() {
    // 延迟一下确保Canvas完全准备好
    setTimeout(() => {
      this.generateAvatar();
    }, 200);
  },

  // 选择模板
  selectTemplate(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      templateIndex: index
    });
    this.generateAvatar();
  },

  // 主标题输入
  onMainTextChange(e) {
    this.setData({
      mainText: e.detail.value
    });
    this.generateAvatar();
  },

  // 副标题输入
  onSubTextChange(e) {
    this.setData({
      subText: e.detail.value
    });
    this.generateAvatar();
  },

  // 选择背景色
  selectBgColor(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      bgColorIndex: index
    });
    this.generateAvatar();
  },

  // 选择文字色
  selectTextColor(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      textColorIndex: index
    });
    this.generateAvatar();
  },

  // 主标题字体大小
  onMainFontSizeChange(e) {
    this.setData({
      mainFontSize: e.detail.value
    });
    // 延迟生成避免频繁刷新
    clearTimeout(this.fontTimer);
    this.fontTimer = setTimeout(() => {
      this.generateAvatar();
    }, 300);
  },

  // 副标题字体大小
  onSubFontSizeChange(e) {
    this.setData({
      subFontSize: e.detail.value
    });
    // 延迟生成避免频繁刷新
    clearTimeout(this.fontTimer);
    this.fontTimer = setTimeout(() => {
      this.generateAvatar();
    }, 300);
  },

  // 边框开关
  onBorderChange(e) {
    this.setData({
      showBorder: e.detail.value
    });
    this.generateAvatar();
  },

  // 阴影开关
  onShadowChange(e) {
    this.setData({
      showShadow: e.detail.value
    });
    this.generateAvatar();
  },

  // 图标开关
  onIconChange(e) {
    this.setData({
      showIcon: e.detail.value
    });
    this.generateAvatar();
  },

  // 生成头像
  generateAvatar() {
    const {
      canvasSize, templateIndex, mainText, subText,
      bgColorIndex, textColorIndex, mainFontSize, subFontSize,
      showBorder, showShadow, showIcon, templates, bgColors, textColors
    } = this.data;

    // 使用Canvas 2D API
    wx.createSelectorQuery()
      .in(this)
      .select('#avatarCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          console.error('无法获取Canvas 2D节点');
          return;
        }

        const canvas = res[0].node;
        const ctx = canvas.getContext('2d');
        const dpr = wx.getSystemInfoSync().pixelRatio;

        // 设置画布尺寸
        canvas.width = canvasSize * dpr;
        canvas.height = canvasSize * dpr;
        ctx.scale(dpr, dpr);

        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);

        // 绘制背景
        this.drawBackground(ctx, canvasSize, bgColors[bgColorIndex]);

        // 绘制边框
        if (showBorder) {
          this.drawBorder(ctx, canvasSize);
        }

        // 绘制图标
        if (showIcon) {
          this.drawIcon(ctx, canvasSize);
        }

        // 绘制文字
        this.drawText(ctx, canvasSize, mainText, subText, textColors[textColorIndex], mainFontSize, subFontSize, showShadow);

        console.log('头像绘制完成');
      });
  },

  // 绘制背景
  drawBackground(ctx, size, color) {
    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, size, size);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, this.adjustBrightness(color, -20));

    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, size, size);

    // 添加纹理效果
    ctx.fillStyle = 'rgba(255, 255, 255, 0.1)';
    for (let i = 0; i < 20; i++) {
      const x = Math.random() * size;
      const y = Math.random() * size;
      const radius = Math.random() * 3 + 1;
      ctx.beginPath();
      ctx.arc(x, y, radius, 0, 2 * Math.PI);
      ctx.fill();
    }
  },

  // 绘制边框
  drawBorder(ctx, size) {
    ctx.strokeStyle = '#ffffff';
    ctx.lineWidth = 6;
    ctx.strokeRect(3, 3, size - 6, size - 6);

    // 内边框
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.5)';
    ctx.lineWidth = 2;
    ctx.strokeRect(12, 12, size - 24, size - 24);
  },

  // 绘制图标
  drawIcon(ctx, size) {
    const centerX = size / 2;
    const iconY = size * 0.25;

    // 绘制皇冠图标
    ctx.fillStyle = '#FFD700';
    ctx.beginPath();
    ctx.moveTo(centerX - 20, iconY);
    ctx.lineTo(centerX - 10, iconY - 15);
    ctx.lineTo(centerX, iconY - 10);
    ctx.lineTo(centerX + 10, iconY - 15);
    ctx.lineTo(centerX + 20, iconY);
    ctx.lineTo(centerX + 15, iconY + 10);
    ctx.lineTo(centerX - 15, iconY + 10);
    ctx.closePath();
    ctx.fill();

    // 皇冠装饰
    ctx.fillStyle = '#FFA500';
    ctx.beginPath();
    ctx.arc(centerX, iconY - 8, 3, 0, 2 * Math.PI);
    ctx.fill();
  },

  // 绘制文字
  drawText(ctx, size, mainText, subText, textColor, mainFontSize, subFontSize, showShadow) {
    const centerX = size / 2;

    // 计算文字位置
    const hasIcon = this.data.showIcon;
    const mainY = hasIcon ? size * 0.55 : size * 0.45;
    const subY = hasIcon ? size * 0.75 : size * 0.65;

    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';

    // 绘制主标题阴影
    if (showShadow) {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
      ctx.font = `bold ${mainFontSize}px sans-serif`;
      ctx.fillText(mainText, centerX + 2, mainY + 2);
    }

    // 绘制主标题
    ctx.fillStyle = textColor;
    ctx.font = `bold ${mainFontSize}px sans-serif`;
    ctx.fillText(mainText, centerX, mainY);

    // 绘制副标题
    if (subText) {
      // 副标题阴影
      if (showShadow) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.3)';
        ctx.font = `${subFontSize}px sans-serif`;
        ctx.fillText(subText, centerX + 1, subY + 1);
      }

      // 副标题
      ctx.fillStyle = textColor;
      ctx.font = `${subFontSize}px sans-serif`;
      ctx.fillText(subText, centerX, subY);
    }
  },

  // 调整颜色亮度
  adjustBrightness(color, amount) {
    const usePound = color[0] === '#';
    const col = usePound ? color.slice(1) : color;
    const num = parseInt(col, 16);
    let r = (num >> 16) + amount;
    let g = (num >> 8 & 0x00FF) + amount;
    let b = (num & 0x0000FF) + amount;

    r = r > 255 ? 255 : r < 0 ? 0 : r;
    g = g > 255 ? 255 : g < 0 ? 0 : g;
    b = b > 255 ? 255 : b < 0 ? 0 : b;

    return (usePound ? '#' : '') + (r << 16 | g << 8 | b).toString(16).padStart(6, '0');
  },

  // 保存头像
  saveAvatar() {
    wx.createSelectorQuery()
      .in(this)
      .select('#avatarCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          wx.showToast({
            title: '无法获取画布',
            icon: 'error'
          });
          return;
        }

        const canvas = res[0].node;

        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                wx.showToast({
                  title: '保存成功',
                  icon: 'success'
                });
              },
              fail: () => {
                wx.showToast({
                  title: '保存失败',
                  icon: 'none'
                });
              }
            });
          },
          fail: () => {
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        }, this);
      });
  },

  // 分享头像
  shareAvatar() {
    wx.createSelectorQuery()
      .in(this)
      .select('#avatarCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res[0] || !res[0].node) {
          wx.showToast({
            title: '无法获取画布',
            icon: 'error'
          });
          return;
        }

        const canvas = res[0].node;

        wx.canvasToTempFilePath({
          canvas: canvas,
          fileType: 'png',
          quality: 1,
          success: (res) => {
            wx.showShareImageMenu({
              path: res.tempFilePath
            });
          },
          fail: () => {
            wx.showToast({
              title: '生成图片失败',
              icon: 'none'
            });
          }
        }, this);
      });
  },

  onShareAppMessage() {
    return {
      title: '团长头像制作器',
      path: '/pages/group_avatar/group_avatar'
    };
  }
});