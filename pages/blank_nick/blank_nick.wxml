<!--pages/blank_nick/blank_nick.wxml-->
<view class="container">
  <!-- 页面标题 -->
  <view class="page-header">
    <text class="page-title">空白昵称生成器</text>
    <text class="page-desc">输入你的昵称，让它变得独一无二，解决重名问题</text>
  </view>

  <!-- 生成设置 -->
  <view class="section">
    <view class="section-title">生成设置</view>
    
    <!-- 基础名称输入 -->
    <view class="form-item">
      <text class="label">你的昵称</text>
      <input 
        class="name-input" 
        type="text" 
        value="{{baseName}}" 
        placeholder="请输入你的昵称"
        bindinput="onBaseNameInput"
        maxlength="6"
      />
    </view>

    <!-- 生成模式选择 -->
    <view class="form-item">
      <text class="label">添加方式</text>
      <picker range="{{generationModes}}" value="{{selectedModeIndex}}" bindchange="onGenerationModeChange">
        <view class="picker">{{generationModes[selectedModeIndex]}}</view>
      </picker>
    </view>
    
    <!-- 字符类型选择 -->
    <view class="form-item">
      <text class="label">字符选择</text>
      <picker range="{{characterTypes}}" value="{{selectedTypeIndex}}" bindchange="onCharacterTypeChange">
        <view class="picker">{{characterTypes[selectedTypeIndex]}}</view>
      </picker>
    </view>

    <!-- 不可见字符数量 -->
    <view class="form-item">
      <text class="label">空白字符数</text>
      <slider min="1" max="10" value="{{invisibleCount}}" bindchange="onInvisibleCountChange" show-value />
    </view>

    <!-- 生成按钮 -->
    <view class="button-group">
      <button type="primary" bindtap="onGenerate">生成昵称</button>
      <button type="default" bindtap="onGenerateRandom">随机生成</button>
    </view>
  </view>

  <!-- 生成结果 -->
  <view class="section">
    <view class="section-title">生成结果</view>
    
    <!-- 结果显示 -->
    <view class="result-container">
      <view class="result-item" wx:for="{{results}}" wx:key="index">
        <view class="result-content">
          <text class="result-text" selectable="true">{{item.text}}</text>
          <text class="result-desc">{{item.description}}</text>
        </view>
        <view class="result-actions">
          <button size="mini" type="default" bindtap="onCopy" data-text="{{item.text}}">复制</button>
          <button size="mini" type="default" bindtap="onTest" data-text="{{item.text}}">测试</button>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{results.length === 0}}">
      <text class="empty-text">点击生成按钮创建空白昵称</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="section">
    <view class="section-title">使用说明</view>
    <view class="help-content">
      <view class="help-item">
        <text class="help-title">📝 输入名称</text>
        <text class="help-desc">输入你想要的昵称，最多6个字符</text>
      </view>
      <view class="help-item">
        <text class="help-title">🎯 选择模式</text>
        <text class="help-desc">选择空白字符的添加方式</text>
      </view>
      <view class="help-item">
        <text class="help-title">✨ 生成昵称</text>
        <text class="help-desc">点击生成按钮，获得多个昵称选项</text>
      </view>
      <view class="help-item">
        <text class="help-title">📋 复制使用</text>
        <text class="help-desc">点击复制按钮，粘贴到游戏或应用中使用</text>
      </view>
    </view>
  </view>

  <!-- 测试区域 -->
  <view class="section" wx:if="{{showTestArea}}">
    <view class="section-title">测试区域</view>
    <view class="test-container">
      <text class="test-label">当前测试昵称：</text>
      <view class="test-display">
        <text class="test-text" selectable="true">{{testText}}</text>
        <view class="test-info">
          <text class="info-item">长度: {{testText.length}} 字符</text>
          <text class="info-item" wx:if="{{testAnalysis}}">字节: {{testAnalysis.byteLength}} bytes</text>
          <text class="info-item" wx:if="{{testAnalysis && testAnalysis.hasVisibleChars}}">⚠️ 包含可见字符</text>
          <text class="info-item" wx:if="{{testAnalysis && !testAnalysis.hasVisibleChars}}">✅ 纯不可见字符</text>
        </view>
        <view class="char-codes" wx:if="{{testAnalysis && testAnalysis.charCodes.length > 0}}">
          <text class="codes-label">字符编码:</text>
          <text class="codes-text">{{testAnalysis.charCodes.join(', ')}}</text>
        </view>
      </view>
      <button type="default" bindtap="onCloseTest">关闭测试</button>
    </view>
  </view>
</view>