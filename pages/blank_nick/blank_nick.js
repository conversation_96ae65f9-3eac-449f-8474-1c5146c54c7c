// pages/blank_nick/blank_nick.js
Page({
  data: {
    // 基础名称
    baseName: '',
    
    // 生成模式选项
    generationModes: ['纯空白昵称', '名称后加空白', '名称前加空白', '字符间加空白'],
    selectedModeIndex: 1,
    
    // 字符类型选项
    characterTypes: ['推荐字符', '其他字符'],
    selectedTypeIndex: 0,
    
    // 不可见字符数量
    invisibleCount: 3,
    
    results: [],
    showTestArea: false,
    testText: '',
    testAnalysis: null,

    // 各种不可见字符
    characters: {
      // 零宽字符 - 完全不可见
      zeroWidth: [
        '\u200B', // 零宽空格
        '\u200C', // 零宽非连字符
        '\u200D', // 零宽连字符
        '\u2060', // 词连接符
        '\uFEFF', // 零宽非断空格
      ],
      
      // 空白字符 - 各种宽度的空白
      whitespace: [
        '\u0020', // 普通空格
        '\u00A0', // 不间断空格
        '\u2000', // En Quad
        '\u2001', // Em Quad
        '\u2002', // En Space
        '\u2003', // Em Space
        '\u2004', // Three-Per-Em Space
        '\u2005', // Four-Per-Em Space
        '\u2006', // Six-Per-Em Space
        '\u2007', // Figure Space
        '\u2008', // Punctuation Space
        '\u2009', // Thin Space
        '\u200A', // Hair Space
      ],
      
      // 特殊空格
      specialSpaces: [
        '\u2028', // 行分隔符
        '\u2029', // 段分隔符
        '\u202F', // 窄不间断空格
        '\u205F', // 中等数学空格
        '\u3000', // 表意文字空格
      ],
      
      // 组合字符
      combining: [
        '\u0300', // 组合重音符
        '\u0301', // 组合锐音符
        '\u0302', // 组合扬抑符
        '\u0303', // 组合波浪号
        '\u0304', // 组合上划线
        '\u0305', // 组合上划线
        '\u0306', // 组合短音符
        '\u0307', // 组合点
      ]
    }
  },

  onLoad(options) {
    // 页面加载时生成一些默认结果
    this.generateDefaultResults();
  },

  // 生成默认结果
  generateDefaultResults() {
    const defaultResults = [
      {
        text: '玩家' + '\u200B\u200B',
        description: '示例：玩家 + 空白字符'
      },
      {
        text: '\u200B\u200B' + '用户',
        description: '示例：空白字符 + 用户'
      },
      {
        text: '游\u200B戏\u200B',
        description: '示例：字符间加空白'
      },
      {
        text: '\u200B\u200B\u200B',
        description: '纯空白昵称 - 完全不可见'
      }
    ];

    this.setData({
      results: defaultResults
    });
  },

  // 基础名称输入
  onBaseNameInput(e) {
    this.setData({
      baseName: e.detail.value
    });
  },

  // 生成模式改变
  onGenerationModeChange(e) {
    this.setData({
      selectedModeIndex: e.detail.value
    });
  },

  // 字符类型改变
  onCharacterTypeChange(e) {
    this.setData({
      selectedTypeIndex: e.detail.value
    });
  },

  // 不可见字符数量改变
  onInvisibleCountChange(e) {
    this.setData({
      invisibleCount: e.detail.value
    });
  },

  // 生成昵称
  onGenerate() {
    const { baseName, selectedModeIndex, selectedTypeIndex, invisibleCount } = this.data;
    const results = [];

    // 根据生成模式生成不同的昵称
    switch (selectedModeIndex) {
      case 0: // 纯空白昵称
        results.push(...this.generatePureInvisible(selectedTypeIndex, invisibleCount));
        break;
      case 1: // 名称后加空白
        if (baseName.trim()) {
          results.push(...this.generateNameWithInvisible(baseName, selectedTypeIndex, invisibleCount, 'suffix'));
        } else {
          results.push(...this.generatePureInvisible(selectedTypeIndex, invisibleCount));
        }
        break;
      case 2: // 名称前加空白
        if (baseName.trim()) {
          results.push(...this.generateNameWithInvisible(baseName, selectedTypeIndex, invisibleCount, 'prefix'));
        } else {
          results.push(...this.generatePureInvisible(selectedTypeIndex, invisibleCount));
        }
        break;
      case 3: // 字符间加空白
        if (baseName.trim()) {
          results.push(...this.generateNameWithCharacterInsertion(baseName, selectedTypeIndex, invisibleCount, 'after'));
        } else {
          results.push(...this.generatePureInvisible(selectedTypeIndex, invisibleCount));
        }
        break;
    }

    if (results.length === 0) {
      wx.showToast({
        title: '请输入你的昵称',
        icon: 'none'
      });
      return;
    }

    this.setData({
      results: results
    });

    wx.showToast({
      title: `生成了 ${results.length} 个昵称`,
      icon: 'success'
    });
  },

  // 随机生成
  onGenerateRandom() {
    const invisibleCount = Math.floor(Math.random() * 5) + 1; // 1-5 随机数量
    const typeIndex = Math.floor(Math.random() * 4); // 随机类型 (不包括全部类型)
    const modeIndex = Math.floor(Math.random() * 4); // 随机模式
    
    // 如果选择了需要名称的模式但没有输入名称，生成随机名称
    if (modeIndex > 0 && !this.data.baseName.trim()) {
      const randomNames = ['用户', '玩家', 'User', 'Player', '匿名', '神秘'];
      const randomName = randomNames[Math.floor(Math.random() * randomNames.length)];
      this.setData({
        baseName: randomName
      });
    }
    
    this.setData({
      invisibleCount: invisibleCount,
      selectedTypeIndex: typeIndex,
      selectedModeIndex: modeIndex
    });

    this.onGenerate();
  },

  // 生成纯不可见字符
  generatePureInvisible(typeIndex, count) {
    const results = [];
    
    if (typeIndex === 0) { // 推荐字符 (零宽字符)
      results.push(...this.generateInvisibleByType('zeroWidth', count));
    } else { // 其他字符
      results.push(...this.generateInvisibleByType('whitespace', count));
      results.push(...this.generateInvisibleByType('specialSpaces', count));
    }
    
    return results;
  },

  // 生成带名称的昵称
  generateNameWithInvisible(baseName, typeIndex, count, position) {
    const results = [];
    const invisibleStrings = this.getInvisibleStrings(typeIndex, count);
    
    invisibleStrings.forEach((invisible, index) => {
      let text = '';
      let description = '';
      
      switch (position) {
        case 'prefix': // 不可见字符+名称
          text = invisible.text + baseName;
          description = `${invisible.description} + "${baseName}"`;
          break;
        case 'suffix': // 名称+不可见字符
          text = baseName + invisible.text;
          description = `"${baseName}" + ${invisible.description}`;
          break;
        case 'surround': // 名称被不可见字符包围
          const halfCount = Math.ceil(count / 2);
          const prefixInvisible = this.generateInvisibleString(typeIndex, halfCount);
          const suffixInvisible = this.generateInvisibleString(typeIndex, count - halfCount);
          text = prefixInvisible + baseName + suffixInvisible;
          description = `"${baseName}" 被${invisible.description}包围`;
          break;
      }
      
      results.push({
        text: text,
        description: description
      });
    });
    
    return results;
  },

  // 生成字符间插入不可见字符的昵称
  generateNameWithCharacterInsertion(baseName, typeIndex, count, position) {
    const results = [];
    const invisibleStrings = this.getInvisibleStrings(typeIndex, count);
    
    invisibleStrings.forEach((invisible, index) => {
      let text = '';
      let description = '';
      const chars = Array.from(baseName); // 支持中文等多字节字符
      
      switch (position) {
        case 'after': // 每个字符后插入
          text = chars.map(char => char + invisible.text).join('');
          description = `"${baseName}" 每个字符后 + ${invisible.description}`;
          break;
        case 'before': // 每个字符前插入
          text = chars.map(char => invisible.text + char).join('');
          description = `"${baseName}" 每个字符前 + ${invisible.description}`;
          break;
        case 'both': // 每个字符前后都插入
          text = chars.map(char => invisible.text + char + invisible.text).join('');
          description = `"${baseName}" 每个字符前后 + ${invisible.description}`;
          break;
      }
      
      results.push({
        text: text,
        description: description
      });
    });
    
    return results;
  },

  // 按类型生成不可见字符
  generateInvisibleByType(type, count) {
    const chars = this.data.characters[type];
    const results = [];
    const typeNames = {
      zeroWidth: '推荐字符',
      whitespace: '其他字符',
      specialSpaces: '其他字符',
      combining: '其他字符'
    };

    // 生成几种不同的组合
    for (let i = 0; i < Math.min(3, chars.length); i++) {
      let text = '';
      for (let j = 0; j < count; j++) {
        const charIndex = (i + j) % chars.length;
        text += chars[charIndex];
      }
      
      results.push({
        text: text,
        description: `${typeNames[type]} - 方案${i + 1}`
      });
    }

    return results;
  },

  // 生成混合不可见字符
  generateMixedInvisible(count) {
    const results = [];
    const allChars = [
      ...this.data.characters.zeroWidth,
      ...this.data.characters.whitespace,
      ...this.data.characters.specialSpaces
    ];

    // 生成几种混合组合
    for (let i = 0; i < 2; i++) {
      let text = '';
      for (let j = 0; j < count; j++) {
        const charIndex = (i * 3 + j) % allChars.length;
        text += allChars[charIndex];
      }
      
      results.push({
        text: text,
        description: `混合字符 - 方案${i + 1}`
      });
    }

    return results;
  },

  // 获取不可见字符串数组
  getInvisibleStrings(typeIndex, count) {
    if (typeIndex === 0) { // 推荐字符
      return this.generateInvisibleByType('zeroWidth', count);
    } else { // 其他字符
      return [
        ...this.generateInvisibleByType('whitespace', count).slice(0, 2),
        ...this.generateInvisibleByType('specialSpaces', count).slice(0, 1)
      ];
    }
  },

  // 生成单个不可见字符串
  generateInvisibleString(typeIndex, count) {
    if (typeIndex === 0) { // 推荐字符
      return '\u200B'.repeat(count);
    } else { // 其他字符
      return '\u2000'.repeat(count);
    }
  },

  // 复制文本
  onCopy(e) {
    const text = e.currentTarget.dataset.text;
    
    wx.setClipboardData({
      data: text,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'error'
        });
      }
    });
  },

  // 测试文本
  onTest(e) {
    const text = e.currentTarget.dataset.text;
    const analysis = this.validateNickname(text);
    
    this.setData({
      showTestArea: true,
      testText: text,
      testAnalysis: analysis
    });

    wx.showToast({
      title: '已加载到测试区域',
      icon: 'success'
    });
  },

  // 关闭测试
  onCloseTest() {
    this.setData({
      showTestArea: false,
      testText: '',
      testAnalysis: null
    });
  },

  // 获取字符的描述信息
  getCharacterInfo(char) {
    const charCode = char.charCodeAt(0);
    const descriptions = {
      0x200B: '零宽空格 (ZWSP)',
      0x200C: '零宽非连字符 (ZWNJ)',
      0x200D: '零宽连字符 (ZWJ)',
      0x2060: '词连接符 (WJ)',
      0xFEFF: '零宽非断空格 (ZWNBSP)',
      0x0020: '普通空格',
      0x00A0: '不间断空格 (NBSP)',
      0x2000: 'En Quad',
      0x2001: 'Em Quad',
      0x2002: 'En Space',
      0x2003: 'Em Space',
      0x2028: '行分隔符',
      0x2029: '段分隔符',
      0x202F: '窄不间断空格',
      0x205F: '中等数学空格',
      0x3000: '表意文字空格'
    };
    
    return descriptions[charCode] || `Unicode: U+${charCode.toString(16).toUpperCase().padStart(4, '0')}`;
  },

  // 生成高级组合
  generateAdvancedCombinations(length) {
    const results = [];
    
    // 最常用的零宽字符组合
    const popularCombos = [
      '\u200B', // 纯零宽空格
      '\u200B\u200C', // 零宽空格 + 零宽非连字符
      '\u200B\u200D', // 零宽空格 + 零宽连字符
      '\u200C\u200D', // 零宽非连字符 + 零宽连字符
      '\u2060\u200B', // 词连接符 + 零宽空格
    ];

    popularCombos.forEach((combo, index) => {
      let text = '';
      for (let i = 0; i < length; i++) {
        text += combo;
      }
      
      results.push({
        text: text,
        description: `高级组合 ${index + 1} - 成功率高`
      });
    });

    return results;
  },

  // 验证生成的文本
  validateNickname(text) {
    // 计算UTF-8字节长度的函数
    const getByteLength = (str) => {
      let byteLength = 0;
      for (let i = 0; i < str.length; i++) {
        const code = str.charCodeAt(i);
        if (code <= 0x7F) {
          byteLength += 1;
        } else if (code <= 0x7FF) {
          byteLength += 2;
        } else if (code <= 0xFFFF) {
          byteLength += 3;
        } else {
          byteLength += 4;
        }
      }
      return byteLength;
    };

    const info = {
      length: text.length,
      byteLength: getByteLength(text),
      hasVisibleChars: /[^\u0000-\u001F\u007F-\u009F\u200B-\u200D\u2060\uFEFF\s]/.test(text),
      charCodes: Array.from(text).map(char => char.charCodeAt(0).toString(16).toUpperCase().padStart(4, '0'))
    };
    
    return info;
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '空白昵称生成器 - 生成不可见昵称',
      path: '/pages/blank_nick/blank_nick',
      imageUrl: ''
    };
  }
});