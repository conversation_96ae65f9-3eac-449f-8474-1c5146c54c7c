/* pages/blank_nick/blank_nick.wxss */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 页面头部 */
.page-header {
  text-align: center;
  margin-bottom: 30rpx;
  padding: 30rpx 20rpx;
  background: linear-gradient(135deg, #4CAF50 0%, #2E7D32 100%);
  border-radius: 16rpx;
  color: white;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 26rpx;
  opacity: 0.9;
  display: block;
}

/* 区块样式 */
.section {
  background: white;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
  width: 100%;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 表单项 */
.form-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.form-item:last-child {
  border-bottom: none;
  margin-bottom: 0;
}

.label {
  font-size: 28rpx;
  color: #666;
  width: 150rpx;
  flex-shrink: 0;
}

.picker {
  flex: 1;
  padding: 15rpx 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}

.name-input {
  flex: 1;
  padding: 15rpx 20rpx;
  background: #fff;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #333;
  margin-left: 20rpx;
}

.name-input:focus {
  border-color: #4CAF50;
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: 20rpx;
  margin-top: 30rpx;
}

.button-group button {
  flex: 1;
  border-radius: 25rpx;
  font-size: 28rpx;
}

/* 结果容器 */
.result-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.result-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 1rpx solid #e9ecef;
}

.result-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.result-text {
  font-size: 28rpx;
  font-family: monospace;
  color: #333;
  background: #fff;
  padding: 8rpx 12rpx;
  border-radius: 6rpx;
  border: 1rpx solid #ddd;
  min-height: 40rpx;
  display: block;
}

.result-desc {
  font-size: 24rpx;
  color: #666;
}

.result-actions {
  display: flex;
  gap: 10rpx;
  flex-shrink: 0;
}

.result-actions button {
  border-radius: 15rpx;
  font-size: 24rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 60rpx 20rpx;
  color: #999;
}

.empty-text {
  font-size: 28rpx;
}

/* 帮助内容 */
.help-content {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.help-item {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.help-title {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.help-desc {
  font-size: 26rpx;
  color: #666;
  padding-left: 20rpx;
}

/* 测试区域 */
.test-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.test-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.test-display {
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border: 2rpx dashed #4CAF50;
}

.test-text {
  font-size: 32rpx;
  font-family: monospace;
  color: #333;
  background: #fff;
  padding: 15rpx;
  border-radius: 8rpx;
  border: 1rpx solid #ddd;
  display: block;
  margin-bottom: 10rpx;
  min-height: 50rpx;
}

.test-info {
  display: flex;
  flex-direction: column;
  gap: 5rpx;
}

.info-item {
  font-size: 24rpx;
  color: #666;
}

.char-codes {
  margin-top: 10rpx;
  padding: 10rpx;
  background: #f0f0f0;
  border-radius: 6rpx;
}

.codes-label {
  font-size: 22rpx;
  color: #888;
  display: block;
  margin-bottom: 5rpx;
}

.codes-text {
  font-size: 20rpx;
  font-family: monospace;
  color: #333;
  word-break: break-all;
}

/* 滑块样式调整 */
slider {
  flex: 1;
  margin-left: 20rpx;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .result-item {
    flex-direction: column;
    align-items: stretch;
    gap: 15rpx;
  }
  
  .result-actions {
    justify-content: center;
  }
}