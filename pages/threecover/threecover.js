// pages/threecover/threecover.js
Page({
  data: {
    selectedImage: '', // 选中的图片路径
    showPreview: false, // 是否显示预览区域
    loading: false, // 加载状态
    loadingText: '处理中...', // 加载文字
    splitImages: [0, 1, 2], // 三联图片数组
    originalImageInfo: null, // 原图信息

    showToast: false, // 是否显示Toast
    toastMessage: '', // Toast消息
    imageScale: 1, // 图片缩放比例
    imageX: 0, // 图片X位置
    imageY: 0, // 图片Y位置
    canvasWidth: 0, // canvas宽度
    canvasHeight: 0, // canvas高度
    touchStartDistance: 0, // 双指起始距离
    touchStartScale: 1, // 触摸开始时的缩放比例
    touchStartX: 0, // 触摸开始时的X位置
    touchStartY: 0, // 触摸开始时的Y位置
    lastTouchX: 0, // 上次触摸X位置
    lastTouchY: 0, // 上次触摸Y位置
    imageObj: null, // 图片对象
  },

  onLoad() {
    // 初始化canvas
    this.initCanvas();
  },

  // 初始化canvas
  initCanvas() {
    const systemInfo = wx.getSystemInfoSync();
    const canvasWidth = systemInfo.windowWidth;
    const canvasHeight = systemInfo.windowWidth * 4 / 9;
    
    this.setData({
      canvasWidth,
      canvasHeight
    });
    
    // 设置canvas尺寸
    const ctx = wx.createCanvasContext('previewCanvas', this);
    ctx.scale(systemInfo.pixelRatio, systemInfo.pixelRatio);
    ctx.draw();
  },

  // 显示提示信息
  showToast(message, theme = 'success') {
    this.setData({
      showToast: true,
      toastMessage: message
    });
    
    // 2秒后自动隐藏
    setTimeout(() => {
      this.setData({
        showToast: false,
        toastMessage: ''
      });
    }, 2000);
  },

  // 选择图片
  selectImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      sizeType: ['original'], // 选择原图
      success: (res) => {
        const tempFile = res.tempFiles[0];
        const tempFilePath = tempFile.tempFilePath;
        
        // 检查文件大小（限制为10MB）
        if (tempFile.size > 10 * 1024 * 1024) {
          this.showToast('图片文件过大，请选择小于10MB的图片', 'warning');
          return;
        }
        
        this.setData({
          selectedImage: tempFilePath,
          showPreview: false
        });
        
        // 获取图片信息
        wx.getImageInfo({
          src: tempFilePath,
          success: (imageInfo) => {
            // 计算初始缩放比例，确保图片能完全覆盖画布
            const { canvasWidth, canvasHeight } = this.data;
            const scaleX = canvasWidth / imageInfo.width;
            const scaleY = canvasHeight / imageInfo.height;
            const initialScale = Math.max(scaleX, scaleY);
            
            // 计算初始位置，居中显示
            const imageWidth = imageInfo.width * initialScale;
            const imageHeight = imageInfo.height * initialScale;
            const initialX = (canvasWidth - imageWidth) / 2;
            const initialY = (canvasHeight - imageHeight) / 2;
            
            // 应用边界限制
            const constrainedPosition = this.constrainImagePosition(initialX, initialY, initialScale);
            
            this.setData({
              originalImageInfo: imageInfo,
              imageScale: initialScale,
              imageX: constrainedPosition.x,
              imageY: constrainedPosition.y
            });
            
            // 绘制图片到canvas
             this.drawImageToCanvas(tempFilePath, imageInfo);
            
            console.log('图片信息:', imageInfo);
            
            // 给出图片尺寸建议
            if (imageInfo.width < 2160 || imageInfo.height < 1280) {
              this.showToast('建议使用至少2160x1280像素的横向图片以获得最佳效果', 'info');
            }
          },
          fail: (err) => {
            console.error('获取图片信息失败:', err);
            this.showToast('获取图片信息失败，请重新选择', 'error');
            this.setData({
              selectedImage: '',
              originalImageInfo: null
            });
          }
        });
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        if (err.errMsg.includes('cancel')) {
          // 用户取消选择，不显示错误提示
          return;
        }
        this.showToast('选择图片失败，请重试', 'error');
      }
    });
  },



  // 绘制图片到canvas
  drawImageToCanvas(imagePath, imageInfo) {
    const { canvasWidth, canvasHeight, imageScale, imageX, imageY } = this.data;
    const ctx = wx.createCanvasContext('previewCanvas', this);
    
    // 清空canvas
    ctx.clearRect(0, 0, canvasWidth, canvasHeight);
    
    // 计算图片绘制位置和尺寸
    const drawWidth = imageInfo.width * imageScale;
    const drawHeight = imageInfo.height * imageScale;
    const drawX = imageX;
    const drawY = imageY;
    
    // 绘制图片
    ctx.drawImage(imagePath, drawX, drawY, drawWidth, drawHeight);
    
    ctx.draw();
  },



  // 触摸开始事件
  onTouchStart(e) {
    const touches = e.touches;
    if (touches.length === 1) {
      // 单指拖拽
      this.setData({
        lastTouchX: touches[0].x,
        lastTouchY: touches[0].y
      });
    } else if (touches.length === 2) {
      // 双指缩放
      const distance = this.getTouchDistance(touches[0], touches[1]);
      this.setData({
        touchStartDistance: distance,
        touchStartScale: this.data.imageScale
      });
    }
  },

  // 触摸移动事件
  onTouchMove(e) {
    const touches = e.touches;
    if (touches.length === 1) {
      // 单指拖拽
      const deltaX = touches[0].x - this.data.lastTouchX;
      const deltaY = touches[0].y - this.data.lastTouchY;
      
      let newX = this.data.imageX + deltaX;
      let newY = this.data.imageY + deltaY;
      
      // 应用边界限制
      const constrainedPosition = this.constrainImagePosition(newX, newY, this.data.imageScale);
      newX = constrainedPosition.x;
      newY = constrainedPosition.y;
      
      this.setData({
        imageX: newX,
        imageY: newY,
        lastTouchX: touches[0].x,
        lastTouchY: touches[0].y
      });
      
      // 重新绘制
      this.drawImageToCanvas(this.data.selectedImage, this.data.originalImageInfo);
    } else if (touches.length === 2) {
      // 双指缩放
      const distance = this.getTouchDistance(touches[0], touches[1]);
      let scale = this.data.touchStartScale * (distance / this.data.touchStartDistance);
      
      // 计算最小缩放比例，确保图片能完全覆盖画布
      const minScale = this.calculateMinScale();
      
      // 限制缩放范围
      const finalScale = Math.max(minScale, Math.min(3, scale));
      
      // 应用缩放后的边界限制
      const constrainedPosition = this.constrainImagePosition(this.data.imageX, this.data.imageY, finalScale);
      
      this.setData({
        imageScale: finalScale,
        imageX: constrainedPosition.x,
        imageY: constrainedPosition.y
      });
      
      // 重新绘制
      this.drawImageToCanvas(this.data.selectedImage, this.data.originalImageInfo);
    }
  },

  // 触摸结束事件
  onTouchEnd(e) {
    // 重置触摸状态
  },

  // 计算两点间距离
  getTouchDistance(touch1, touch2) {
    const dx = touch1.x - touch2.x;
    const dy = touch1.y - touch2.y;
    return Math.sqrt(dx * dx + dy * dy);
  },

  // 计算最小缩放比例，确保图片能完全覆盖画布
  calculateMinScale() {
    const { originalImageInfo, canvasWidth, canvasHeight } = this.data;
    if (!originalImageInfo) return 1;
    
    const imageWidth = originalImageInfo.width;
    const imageHeight = originalImageInfo.height;
    
    // 计算能完全覆盖画布所需的最小缩放比例
    const scaleX = canvasWidth / imageWidth;
    const scaleY = canvasHeight / imageHeight;
    
    // 取较大值确保图片能完全覆盖画布
    return Math.max(scaleX, scaleY);
  },

  // 限制图片位置，确保不向内溢出画布边界
  constrainImagePosition(x, y, scale) {
    const { originalImageInfo, canvasWidth, canvasHeight } = this.data;
    if (!originalImageInfo) return { x, y };
    
    const imageWidth = originalImageInfo.width * scale;
    const imageHeight = originalImageInfo.height * scale;
    
    // 计算边界限制
    // 图片右边缘不能小于画布右边缘
    const maxX = 0;
    const minX = canvasWidth - imageWidth;
    
    // 图片下边缘不能小于画布下边缘
    const maxY = 0;
    const minY = canvasHeight - imageHeight;
    
    // 应用限制
    const constrainedX = Math.max(minX, Math.min(maxX, x));
    const constrainedY = Math.max(minY, Math.min(maxY, y));
    
    return {
      x: constrainedX,
      y: constrainedY
    };
  },

  // 生成分割图片
  generateSplitImages(imagePath) {
    const { originalImageInfo, imageScale, imageX, imageY, canvasWidth, canvasHeight } = this.data;
    
    // 检查图片信息
    if (!originalImageInfo) {
      this.setData({ loading: false });
      this.showToast('图片信息获取失败', 'error');
      return;
    }
    
    const originalWidth = originalImageInfo.width;
    const originalHeight = originalImageInfo.height;
    
    // 计算canvas中可见区域在原图中的位置和尺寸
    // canvas坐标系：imageX, imageY是图片左上角在canvas中的位置
    const visibleLeft = Math.max(0, -imageX / imageScale);
    const visibleTop = Math.max(0, -imageY / imageScale);
    const visibleRight = Math.min(originalWidth, visibleLeft + canvasWidth / imageScale);
    const visibleBottom = Math.min(originalHeight, visibleTop + canvasHeight / imageScale);
    
    const visibleWidth = visibleRight - visibleLeft;
    const visibleHeight = visibleBottom - visibleTop;
    
    // 检查可见区域是否足够大
    const minWidth = canvasWidth / imageScale * 0.3;
    const minHeight = canvasHeight / imageScale * 0.3;
    if (visibleWidth < minWidth || visibleHeight < minHeight) {
      this.setData({ loading: false });
      this.showToast('当前可见区域过小，请调整图片位置和缩放', 'warning');
      return;
    }
    
    this.showToast('图片已选择，可以直接保存三联封面');
  },

  // 保存所有图片
  saveAllImages() {
    if (!this.data.selectedImage) {
      this.showToast('请先选择图片', 'warning');
      return;
    }

    // 显示确认对话框
    wx.showModal({
      title: '保存确认',
      content: '将保存3张720x960像素的三联封面图片到相册',
      confirmText: '保存',
      cancelText: '取消',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            loading: true,
            loadingText: '正在保存图片...'
          });

          // 请求保存到相册权限
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              this.saveImagesSequentially();
            },
            fail: () => {
              // 权限被拒绝，引导用户手动开启
              wx.showModal({
                title: '需要相册权限',
                content: '需要获取您的相册权限来保存图片，请在设置中开启',
                confirmText: '去设置',
                cancelText: '取消',
                success: (modalRes) => {
                  if (modalRes.confirm) {
                    wx.openSetting({
                      success: (settingRes) => {
                        if (settingRes.authSetting['scope.writePhotosAlbum']) {
                          this.saveImagesSequentially();
                        } else {
                          this.setData({ loading: false });
                          this.showToast('未获得相册权限，无法保存图片', 'error');
                        }
                      }
                    });
                  } else {
                    this.setData({ loading: false });
                  }
                }
              });
            }
          });
        }
      }
    });
  },

  // 依次保存图片
  saveImagesSequentially() {
    const { selectedImage, originalImageInfo, imageScale, imageX, imageY, canvasWidth, canvasHeight } = this.data;
    const targetWidth = 720;
      const targetHeight = 960;
    let savedCount = 0;
    let failedCount = 0;
    
    // 计算可见区域参数
    const originalWidth = originalImageInfo.width;
    const originalHeight = originalImageInfo.height;
    
    // 计算canvas中可见区域在原图中的位置和尺寸
    const visibleLeft = Math.max(0, -imageX / imageScale);
    const visibleTop = Math.max(0, -imageY / imageScale);
    const visibleRight = Math.min(originalWidth, visibleLeft + canvasWidth / imageScale);
    const visibleBottom = Math.min(originalHeight, visibleTop + canvasHeight / imageScale);
    
    const visibleWidth = visibleRight - visibleLeft;
    const visibleHeight = visibleBottom - visibleTop;
    
    // 保存每张图片
    for (let i = 0; i < 3; i++) {
      this.setData({
        loadingText: `正在保存第${i + 1}张图片...`
      });
      
      this.saveHighQualityImage(selectedImage, i, {
        sourceX: visibleLeft + (visibleWidth / 3) * i,
        sourceY: visibleTop,
        sourceWidth: visibleWidth / 3,
        sourceHeight: visibleHeight,
        targetWidth,
        targetHeight
      }, (success) => {
        if (success) {
          savedCount++;
        } else {
          failedCount++;
        }
        
        if (savedCount + failedCount === 3) {
          this.setData({ loading: false });
          
          if (savedCount === 3) {
            this.showToast('所有图片保存成功！');
          } else if (savedCount > 0) {
            this.showToast(`成功保存${savedCount}张图片，${failedCount}张失败`, 'warning');
          } else {
            this.showToast('图片保存失败，请重试', 'error');
          }
        }
      });
    }
  },

  // 保存高质量图片
  saveHighQualityImage(imagePath, index, params, callback) {
    const canvasId = `saveCanvas${index}`;
    
    try {
      // 创建临时canvas用于高质量输出
      const ctx = wx.createCanvasContext(canvasId, this);
      const { sourceX, sourceY, sourceWidth, sourceHeight, targetWidth, targetHeight } = params;
      
      // 绘制图片
      ctx.drawImage(
        imagePath,
        sourceX, sourceY, sourceWidth, sourceHeight,
        0, 0, targetWidth, targetHeight
      );
      
      ctx.draw(false, () => {
        // 生成带时间戳和序号的文件名
        const timestamp = new Date().getTime();
        const paddedIndex = String(index + 1).padStart(2, '0');
        
        wx.canvasToTempFilePath({
          canvasId: canvasId,
          width: targetWidth,
          height: targetHeight,
          destWidth: targetWidth,
          destHeight: targetHeight,
          quality: 1.0, // 最高质量
          success: (res) => {
            // 使用文件管理器保存文件，确保文件名唯一
            const fs = wx.getFileSystemManager();
            const fileName = `抖音封面_${timestamp}_${paddedIndex}.jpg`;
            
            wx.saveImageToPhotosAlbum({
              filePath: res.tempFilePath,
              success: () => {
                console.log(`图片${paddedIndex}保存成功: ${fileName}`);
                callback && callback(true);
              },
              fail: (err) => {
                console.error(`图片${paddedIndex}保存失败:`, err);
                callback && callback(false);
              }
            });
          },
          fail: (err) => {
            console.error(`生成图片${paddedIndex}失败:`, err);
            callback && callback(false);
          }
        }, this);
      });
    } catch (error) {
      console.error(`处理图片${index + 1}时发生错误:`, error);
      callback && callback(false);
    }
  },



  // 图片加载完成
  onImageLoad(e) {
    console.log('图片加载完成:', e.detail);
    const { originalImageInfo } = this.data;
    
    if (originalImageInfo) {
      // 获取预览区域尺寸（固定为720px宽度）
      const previewWidth = 720;
      const imageWidth = originalImageInfo.width;
      
      // 计算初始缩放比例，让图片宽度适配预览区域
      const initialScale = previewWidth / imageWidth;
      
      // 限制缩放范围在0.1到3之间
      const scale = Math.max(0.1, Math.min(3, initialScale));
      
      this.setData({
        imageScale: scale,
        imageX: 0,
        imageY: 0
      });
    } else {
      // 如果没有图片信息，使用默认值
      this.setData({
        imageScale: 1,
        imageX: 0,
        imageY: 0
      });
    }
  },

  // 图片缩放事件
  onImageScale(e) {
    const { scale } = e.detail;
    this.setData({
      imageScale: scale
    });
  },

  // 图片移动事件
  onImageMove(e) {
    const { x, y } = e.detail;
    this.setData({
      imageX: x,
      imageY: y
    });
  },



  // 重新制作
  reset() {
    this.setData({
      selectedImage: '',
      showPreview: false,
      loading: false,
      originalImageInfo: null,
      showToast: false,
      toastMessage: '',
      imageScale: 1,
      imageX: 0,
      imageY: 0
    });
  }
});
