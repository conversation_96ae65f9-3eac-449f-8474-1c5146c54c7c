/* pages/threecover/threecover.wxss */
page {
  background-color: #f5f5f5;
  height: 100vh;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 标题区域 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 40rpx 20rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

/* 上传区域 */
.upload-section {
  margin-bottom: 40rpx;
}

.upload-area {
  background: #fff;
  border: 2rpx dashed #ddd;
  border-radius: 16rpx;
  padding: 80rpx 40rpx;
  text-align: center;
}

.upload-area:active {
  background: #f8f8f8;
  border-color: #007aff;
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.upload-text {
  display: block;
  font-size: 32rpx;
  color: #333;
  margin-bottom: 16rpx;
  font-weight: 500;
}

.upload-hint {
  display: block;
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 图片预览 */
.image-preview {
  position: relative;
  width: 100%;
  margin-bottom: 30rpx;
}

.canvas-container {
  width: 100%;
  height: calc(100vw * 4 / 9);
  background: #f8f9fa;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.preview-canvas {
  width: 100%;
  height: 100%;
  display: block;
}

/* 预裁剪虚线 */
.crop-lines {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 10;
}

.crop-lines::before,
.crop-lines::after {
  content: '';
  position: absolute;
  top: 0;
  bottom: 0;
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    #ff6b6b 0,
    #ff6b6b 8rpx,
    transparent 8rpx,
    transparent 16rpx
  );
}

.crop-lines::before {
  left: 33.33%;
}

.crop-lines::after {
  left: 66.66%;
}



.image-tips {
  text-align: center;
  margin-top: 20rpx;
  margin-bottom: 10rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
  background: rgba(0, 0, 0, 0.05);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}



/* 按钮样式 */
.btn {
  padding: 16rpx 32rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  border: none;
  cursor: pointer;
}

.btn-primary {
  background: #007aff;
  color: white;
}

.btn-primary:hover {
  background: #0056cc;
}

.btn-outline {
  background: transparent;
  color: #007aff;
  border: 2rpx solid #007aff;
}

.btn-outline:hover {
  background: #007aff;
  color: white;
}

.btn-large {
  padding: 24rpx 48rpx;
  font-size: 32rpx;
  width: 100%;
  margin-bottom: 20rpx;
}

/* 加载状态样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-content {
  background: white;
  padding: 40rpx;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* Toast样式 */
.toast-overlay {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 10000;
  pointer-events: none;
}

.toast-content {
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  max-width: 500rpx;
  text-align: center;
  animation: fadeInOut 2s ease-in-out;
}

@keyframes fadeInOut {
  0%, 100% { opacity: 0; }
  10%, 90% { opacity: 1; }
}

.image-actions {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

/* 预览区域 */
.preview-section {
  margin-top: 40rpx;
}

.preview-header {
  margin-bottom: 30rpx;
}

.preview-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

/* 分割预览 */
.split-preview {
  margin-bottom: 40rpx;
}

.split-container {
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 2rpx 12rpx rgba(0,0,0,0.1);
}

.preview-canvas {
  display: block;
  margin: 0 auto;
  border-radius: 8rpx;
}

/* 分割线样式 */
.split-line {
  position: absolute;
  top: 20rpx;
  bottom: 20rpx;
  width: 2rpx;
  background: repeating-linear-gradient(
    to bottom,
    #ff6b6b 0,
    #ff6b6b 8rpx,
    transparent 8rpx,
    transparent 16rpx
  );
  z-index: 10;
}

.split-line-1 {
  left: calc(33.33% + 20rpx);
}

.split-line-2 {
  left: calc(66.66% + 20rpx);
}

/* 三联图片预览 */
.triple-preview {
  margin-bottom: 40rpx;
}

.triple-scroll {
  white-space: nowrap;
}

.triple-container {
  display: flex;
  gap: 20rpx;
  padding: 20rpx;
}

.triple-item {
  flex-shrink: 0;
  background: #fff;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.triple-canvas {
  display: block;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
}

.triple-label {
  font-size: 24rpx;
  color: #666;
  font-weight: 500;
}

/* 操作按钮 */
.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  padding: 0 20rpx;
}

/* 加载状态 */
t-loading {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1000;
}

/* 响应式适配 */
@media (max-width: 375px) {
  .title {
    font-size: 42rpx;
  }
  
  .upload-area {
    padding: 60rpx 30rpx;
  }
  
  .upload-icon {
    font-size: 60rpx;
  }
}
