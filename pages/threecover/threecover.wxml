<!--pages/threecover/threecover.wxml-->
<view class="container">
  <!-- 标题区域 -->
  <view class="header">
    <text class="title">抖音三联封面制作</text>
    <text class="subtitle">上传图片，自动分割成3张连续封面</text>
  </view>

  <!-- 图片选择区域 -->
  <view class="image-section">
    <view wx:if="{{!selectedImage}}" class="upload-area" bindtap="selectImage">
      <view class="upload-icon">📷</view>
      <text class="upload-text">点击选择图片</text>
      <text class="upload-hint">支持JPG、PNG格式，建议横向图片</text>
    </view>
    
    <view wx:else class="image-container">
      <!-- 4:9比例的图片预览区域 -->
      <view class="image-preview-area">
        <view class="canvas-container">
          <canvas 
            canvas-id="previewCanvas" 
            class="preview-canvas"
            bindtouchstart="onTouchStart"
            bindtouchmove="onTouchMove"
            bindtouchend="onTouchEnd"
            disable-scroll="true">
          </canvas>
          
          <!-- 预裁剪虚线 -->
          <view class="crop-lines"></view>
        </view>
        
        <!-- 操作提示 -->
        <view class="image-tips">
          <text class="tip-text">双指缩放 · 拖拽移动</text>
        </view>
      </view>
      

      
      <view class="image-actions">
        <button class="btn btn-outline" bindtap="selectImage">重新选择</button>
        <button class="btn btn-primary" bindtap="saveAllImages">保存三联封面</button>
      </view>
    </view>
  </view>



  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-overlay">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text">{{loadingText}}</text>
    </view>
  </view>
</view>

<!-- Toast提示 -->
  <view wx:if="{{showToast}}" class="toast-overlay">
    <view class="toast-content">{{toastMessage}}</view>
  </view>
  
  <!-- 隐藏的高质量保存canvas -->
  <canvas canvas-id="saveCanvas0" style="position: fixed; top: -9999px; left: -9999px; width: 720px; height: 960px;"></canvas>
  <canvas canvas-id="saveCanvas1" style="position: fixed; top: -9999px; left: -9999px; width: 720px; height: 960px;"></canvas>
  <canvas canvas-id="saveCanvas2" style="position: fixed; top: -9999px; left: -9999px; width: 720px; height: 960px;"></canvas>
  
  <!-- 滤镜预览canvas -->
  <canvas canvas-id="filterCanvas" style="position: fixed; top: -9999px; left: -9999px; width: 300px; height: 400px;"></canvas>
