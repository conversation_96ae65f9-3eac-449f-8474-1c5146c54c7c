<!-- index.wxml -->
<view class="container">
  <!-- 自定义导航栏 -->
  <!-- <view class="custom-nav" style="height: {{navHeight}}px;">
    <view class="nav-title" style="padding-top: {{statusBarHeight/2}}px;">隐藏文字工具</view>
  </view> -->
  <!-- 画布区域 -->
  <view class="canvas-container">
    <canvas type="2d" id="hiddenTextCanvas" class="canvas" style="width: {{canvasWidth}}px; height: {{canvasHeight}}px;" bindtouchstart="onTouchStart" bindtouchmove="onTouchMove" bindtouchend="onTouchEnd"></canvas>
  </view>
  <!-- 控制面板 -->
  <scroll-view class="control-panel" scroll-y enable-flex enhanced show-scrollbar="true" fast-deceleration="true" bounces="true" scroll-anchoring="true">
    <!-- 文字设置 -->
    <view class="section">
      <view class="section-title">文字设置</view>
      <view class="form-item">
        <textarea class="textarea" placeholder="请输入要隐藏的文字" value="{{textContent}}" bindinput="onTextInput"></textarea>
      </view>
      <view class="form-item">
        <button type="default" bindtap="onSelectBackgroundImage">选择图片</button>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">背景图片透明度</text>
          <slider min="0" max="1" step="0.01" value="{{backgroundOpacity}}" bindchange="onBackgroundOpacityChange" show-value />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">字体大小</text>
          <slider min="14" max="128" value="{{fontSize}}" bindchange="onFontSizeChange" show-value />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">行间距</text>
          <slider min="1" max="3" step="0.1" value="{{lineHeight}}" bindchange="onLineHeightChange" show-value />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">字间距</text>
          <slider min="0" max="10" value="{{letterSpacing}}" bindchange="onLetterSpacingChange" show-value />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">对齐方式</text>
          <picker range="{{alignOptions}}" value="{{alignIndex}}" bindchange="onAlignChange">
            <view class="picker">{{alignOptions[alignIndex]}}</view>
          </picker>
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">文字颜色</text>
          <color-picker color="{{textColor}}" preset-colors="{{presetColors}}" bind:colorchange="onTextColorChange" bind:toggle="onColorPickerToggle" data-type="text" />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">背景颜色</text>
          <color-picker color="{{backgroundColor}}" preset-colors="{{presetColors}}" bind:colorchange="onBackgroundColorChange" bind:toggle="onColorPickerToggle" data-type="background" />
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">加粗</text>
          <switch checked="{{isBold}}" bindchange="onBoldChange"></switch>
        </view>
        <view class="form-item">
          <text class="label">模糊</text>
          <switch checked="{{isBlur}}" bindchange="onBlurChange"></switch>
        </view>
      </view>
    </view>
    <!-- 干扰线设置 -->
    <view class="section">
      <view class="section-title">干扰线设置</view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">启用干扰线</text>
          <switch checked="{{enableInterference}}" bindchange="onInterferenceToggle"></switch>
        </view>
      </view>
      <block wx:if="{{enableInterference}}">
        <!-- 图案类型选择器 -->
        <view class="form-row">
          <view class="form-item">
            <text class="label">图案类型</text>
            <picker range="{{patternOptions}}" value="{{patternIndex}}" bindchange="onPatternChange">
              <view class="picker">{{patternOptions[patternIndex]}}</view>
            </picker>
          </view>
        </view>
        <!-- 线颜色设置 -->
        <view class="form-row">
          <view class="form-item">
            <text class="label">线颜色</text>
            <color-picker color="{{lineColor}}" preset-colors="{{presetColors}}" bind:colorchange="onLineColorChange" bind:toggle="onColorPickerToggle" data-type="line" />
          </view>
        </view>
        <!-- 动态参数表单 -->
        <view wx:for="{{currentPatternConfig}}" wx:key="key" class="dynamic-param-item">
          <!-- 滑块参数 -->
          <view wx:if="{{item.type === 'slider'}}" class="form-row">
            <view class="form-item">
              <text class="label">{{item.label}}</text>
              <slider min="{{item.min}}" max="{{item.max}}" step="{{item.step}}" value="{{patternParams[item.key]}}" bindchange="onPatternParamChange" data-key="{{item.key}}" show-value />
            </view>
          </view>
          <!-- 选择器参数 -->
          <view wx:elif="{{item.type === 'picker'}}" class="form-row">
            <view class="form-item">
              <text class="label">{{item.label}}</text>
              <picker range="{{item.options}}" value="{{patternParams[item.key + 'Index'] || 0}}" bindchange="onPatternParamChange" data-key="{{item.key}}">
                <view class="picker">{{item.options[patternParams[item.key + 'Index'] || 0]}}</view>
              </picker>
            </view>
          </view>
          <!-- 开关参数 -->
          <view wx:elif="{{item.type === 'switch'}}" class="form-row">
            <view class="form-item">
              <text class="label">{{item.label}}</text>
              <switch checked="{{patternParams[item.key]}}" bindchange="onPatternParamChange" data-key="{{item.key}}"></switch>
            </view>
          </view>
          <!-- 颜色参数 -->
          <view wx:elif="{{item.type === 'color'}}" class="form-row">
            <view class="form-item">
              <text class="label">{{item.label}}</text>
              <color-picker color="{{patternParams[item.key]}}" preset-colors="{{presetColors}}" bind:colorchange="onPatternColorChange" bind:toggle="onColorPickerToggle" data-key="{{item.key}}" data-type="pattern-{{item.key}}" />
            </view>
          </view>
        </view>
      </block>
    </view>
    <!-- 导出设置 -->
    <view class="section">
      <view class="section-title">导出设置</view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">图片格式</text>
          <picker range="{{formatOptions}}" value="{{formatIndex}}" bindchange="onFormatChange">
            <view class="picker">{{formatOptions[formatIndex]}}</view>
          </picker>
        </view>
      </view>
      <view class="form-row">
        <view class="form-item">
          <text class="label">图片质量</text>
          <slider min="0.1" max="1" step="0.1" value="{{quality}}" bindchange="onQualityChange" show-value />
        </view>
      </view>
      <view class="button-group">
        <button type="default" bindtap="onPreview">预览</button>
        <button type="primary" bindtap="onExport">导出图片</button>
      </view>
    </view>
  </scroll-view>
</view>