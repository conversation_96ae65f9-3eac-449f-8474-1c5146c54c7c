// index.js - HiddenTextTool 隐藏文字生成工具
const PatternManager = require('../../utils/PatternManager');

Page({
  data: {
    // 文字设置
    textContent: '这是一段\n隐藏文字\n可以输入\n多行内容',
    fontSize: 50,
    lineHeight: 1.5,
    letterSpacing: 2,
    textColor: '#000000',
    backgroundColor: '#ffffff',
    backgroundImage: '', // 新增背景图片字段
    backgroundOpacity: 1, // 新增背景图片透明度字段，默认不透明
    isBold: true,
    isBlur: false,
    alignOptions: ['左对齐', '居中对齐', '右对齐'],
    alignIndex: 1,

    // 干扰线设置
    enableInterference: true,
    availablePatterns: [], // 可用图案列表
    patternOptions: [], // 图案选项（显示名称）
    patternIndex: 0,
    currentPatternKey: 'zigzag', // 当前选中的图案key
    currentPatternConfig: [], // 当前图案的参数配置
    patternParams: {}, // 当前图案的参数值
    lineColor: '#000000',

    // 导出设置
    formatOptions: ['PNG', 'JPEG'],
    formatIndex: 0,

    quality: 0.9,

    // 画布相关
    canvasWidth: 375,
    canvasHeight: 375,
    textX: 187.5,
    textY: 187.5,
    isDragging: false,
    lastTouchX: 0,
    lastTouchY: 0,

    // 导航栏相关
    statusBarHeight: 20,
    navHeight: 66,

    // 颜色选择器相关
    presetColors: [
      '#000000', '#333333', '#666666', '#999999', '#cccccc', '#ffffff',
      '#ff0000', '#ff6600', '#ffcc00', '#66ff00', '#00ff66', '#00ffcc',
      '#0066ff', '#6600ff', '#cc00ff', '#ff0066', '#ff3333', '#33ff33',
      '#3333ff', '#ffff33', '#ff33ff', '#33ffff', '#800000', '#008000',
      '#000080', '#808000', '#800080', '#008080', '#c0c0c0', '#808080'
    ],
    pattern: 'zigzag'
  },

  // 选择背景图片
  onSelectBackgroundImage() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath;
        this.setData({
          backgroundImage: tempFilePath
        }, () => {
          this.drawCanvas(); // 选择图片后重新绘制画布
        });
      },
      fail: (err) => {
        console.error('选择图片失败', err);
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 背景图片透明度改变
  onBackgroundOpacityChange(e) {
    this.setData({
      backgroundOpacity: e.detail.value
    });
    this.drawCanvas();
  },



  // 初始化图案系统
  initPatterns(selectedPatternKey) {
    const patternsInfo = this.patternManager.getAllPatternsInfo();
    const patternOptions = patternsInfo.map(p => p.name);
    const availablePatterns = patternsInfo;

    // 如果有传入的图案类型，使用它；否则使用默认的锯齿线
    const defaultPattern = selectedPatternKey ?
      patternsInfo.find(p => p.key === selectedPatternKey) || patternsInfo.find(p => p.key === 'zigzag') || patternsInfo[0] :
      patternsInfo.find(p => p.key === 'zigzag') || patternsInfo[0];
    const defaultParams = {};

    // 初始化默认参数值
    defaultPattern.config.forEach(param => {
      if (param.type === 'picker') {
        // 对于picker类型，设置默认值和索引
        const defaultValue = param.values ? param.values[0] : param.options[0];
        defaultParams[param.key] = param.default || defaultValue;
        defaultParams[param.key + 'Index'] = 0;

        // 如果有预定义的默认值，找到对应的索引
        if (param.default) {
          const defaultIndex = param.values ?
            param.values.indexOf(param.default) :
            param.options.indexOf(param.default);
          if (defaultIndex !== -1) {
            defaultParams[param.key + 'Index'] = defaultIndex;
          }
        }
      } else if (param.type === 'color') {
        // 对于color类型，设置默认颜色值
        defaultParams[param.key] = param.default || '#000000';
      } else {
        defaultParams[param.key] = param.default;
      }
    });

    // 找到默认图案在列表中的索引
    const defaultPatternIndex = patternsInfo.findIndex(p => p.key === defaultPattern.key);

    this.setData({
      availablePatterns,
      patternOptions,
      patternIndex: defaultPatternIndex,
      currentPatternKey: defaultPattern.key,
      currentPatternConfig: defaultPattern.config,
      patternParams: defaultParams
    });

    // 创建图案实例
    this.updateCurrentPattern();
  },

  // 更新当前图案实例
  updateCurrentPattern() {
    const params = {
      ...this.data.patternParams,
      lineColor: this.data.lineColor
    };

    this.currentPattern = this.patternManager.create(this.data.currentPatternKey, params);
  },

  onLoad(options) {
    // 加载外部字体
    wx.loadFontFace({
      family: 'Source Han Sans K',
      source: 'url("http://www.addondev.cn/assets/fonts/SourceHanSansK-Regular.ttf")',
      success(res) {
        console.log('字体加载成功', res);
      },
      fail(res) {
        console.error('字体加载失败', res);
      }
    });

    // 获取系统信息，适配不同设备的状态栏高度
    try {
      // 使用新的 API 获取窗口信息
      const windowInfo = wx.getWindowInfo();
      // 使用新的 API 获取设备信息
      const deviceInfo = wx.getDeviceInfo();

      const navHeight = windowInfo.statusBarHeight + 46;

      this.setData({
        statusBarHeight: windowInfo.statusBarHeight,
        navHeight: navHeight,
        screenWidth: windowInfo.screenWidth,
        screenHeight: windowInfo.screenHeight,
        pixelRatio: windowInfo.pixelRatio,
        deviceBrand: deviceInfo.brand,
        deviceModel: deviceInfo.model,
        pattern: options.pattern ?? 'zigzag'
      });

      // 设置导航栏高度和画布尺寸的 CSS 变量
      wx.nextTick(() => {
        const navHeightInRpx = navHeight * 2; // px 转换为 rpx (假设比例为 2)
        const canvasSize = Math.min(windowInfo.screenWidth, 375);
        const canvasSizeInRpx = canvasSize * 2; // px 转换为 rpx

        wx.createSelectorQuery()
          .select('.container')
          .node()
          .exec((res) => {
            if (res && res[0] && res[0].node) {
              res[0].node.style.setProperty('--nav-height', navHeightInRpx + 'rpx');
              res[0].node.style.setProperty('--canvas-size', canvasSizeInRpx + 'rpx');
            }
          });
      });

      console.log('窗口信息:', windowInfo);
      console.log('设备信息:', deviceInfo);
    } catch (error) {
      console.error('获取系统信息失败:', error);
      // 设置默认值
      this.setData({
        statusBarHeight: 20,
        navHeight: 66
      });
    }



    this.initCanvas();
    this.patternManager = new PatternManager();
    this.initPatterns(options.pattern);
  },

  onReady() {
    // Canvas 初始化时已经调用了 drawCanvas，这里不需要重复调用

    // 监听屏幕旋转事件
    wx.onWindowResize((res) => {
      console.log('屏幕尺寸变化:', res);
      // 重新初始化画布
      this.initCanvas();
    });
  },

  onUnload() {
    // 页面卸载时，取消屏幕旋转监听
    wx.offWindowResize();
  },

  // 初始化画布
  initCanvas() {
    // 获取屏幕宽度
    const screenWidth = this.data.screenWidth || wx.getWindowInfo().screenWidth;
    // 计算画布尺寸，使用屏幕宽度，保持正方形
    const canvasSize = Math.min(screenWidth, 375); // 限制最大尺寸为375

    // 更新数据
    this.setData({
      canvasWidth: canvasSize,
      canvasHeight: canvasSize,
      textX: canvasSize / 2, // 文字居中
      textY: canvasSize / 2  // 文字居中
    });

    const query = wx.createSelectorQuery();
    query.select('#hiddenTextCanvas')
      .fields({ node: true, size: true })
      .exec((res) => {
        if (!res || !res[0] || !res[0].node) {
          console.error('获取Canvas节点失败');
          return;
        }

        const canvas = res[0].node;
        const pixelRatio = this.data.pixelRatio || wx.getWindowInfo().pixelRatio || 1;

        // 设置画布的实际渲染尺寸
        canvas.width = canvasSize * pixelRatio;
        canvas.height = canvasSize * pixelRatio;

        const ctx = canvas.getContext('2d');
        // 根据像素比例缩放画布
        ctx.scale(pixelRatio, pixelRatio);

        this.canvas = canvas;
        this.ctx = ctx;

        console.log('Canvas 初始化完成:', {
          canvasSize,
          pixelRatio,
          width: canvas.width,
          height: canvas.height
        });

        // 初始化完成后绘制
        this.drawCanvas();
      });
  },

  // 绘制画布内容
  drawCanvas() {
    if (!this.ctx) return;

    const { canvasWidth, canvasHeight, backgroundColor, backgroundImage, backgroundOpacity } = this.data;

        // 清空画布
        this.ctx.clearRect(0, 0, canvasWidth, canvasHeight);

        // 绘制背景或背景图片
        if (backgroundImage) {
          const img = this.canvas.createImage();
          img.src = backgroundImage;
          img.onload = () => {
            // 保存当前画布状态
            this.ctx.save();
            // 设置透明度
            this.ctx.globalAlpha = backgroundOpacity;

            // 计算图片在画布上的绘制尺寸和位置，以覆盖整个画布
            const imgAspectRatio = img.width / img.height;
            const canvasAspectRatio = canvasWidth / canvasHeight;

            let drawWidth, drawHeight, offsetX, offsetY;

            if (imgAspectRatio > canvasAspectRatio) {
              // 图片更宽，按高度适配，宽度裁剪
              drawHeight = canvasHeight;
              drawWidth = canvasHeight * imgAspectRatio;
              offsetX = (canvasWidth - drawWidth) / 2;
              offsetY = 0;
            } else {
              // 图片更高或等比例，按宽度适配，高度裁剪
              drawWidth = canvasWidth;
              drawHeight = canvasWidth / imgAspectRatio;
              offsetX = 0;
              offsetY = (canvasHeight - drawHeight) / 2;
            }

            this.ctx.drawImage(img, offsetX, offsetY, drawWidth, drawHeight);

            // 恢复画布状态，避免影响后续绘制
            this.ctx.restore();

            // 绘制文字和干扰线
            this.drawText();
            if (this.data.enableInterference) {
              this.drawInterferenceLines();
            }
          };
          img.onerror = (err) => {
            console.error('背景图片加载失败', err);
            // 图片加载失败时，回退到绘制背景颜色
            this.ctx.fillStyle = backgroundColor;
            this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);
            // 绘制文字和干扰线
            this.drawText();
            if (this.data.enableInterference) {
              this.drawInterferenceLines();
            }
          };
        } else {
          // 没有背景图片时，绘制背景颜色
          this.ctx.fillStyle = backgroundColor;
          this.ctx.fillRect(0, 0, canvasWidth, canvasHeight);
          // 绘制文字和干扰线
          this.drawText();
          if (this.data.enableInterference) {
            this.drawInterferenceLines();
          }
        }

    // 先绘制文字，再绘制干扰线，这样干扰线会覆盖在文字上面
    // 绘制文字
    this.drawText();

    // 绘制干扰线
    if (this.data.enableInterference) {
      this.drawInterferenceLines();
    }
  },

  // 绘制干扰线
  drawInterferenceLines() {
    if (!this.currentPattern) return;

    // 更新图案参数
    const params = {
      ...this.data.patternParams,
      lineColor: this.data.lineColor
    };

    this.currentPattern.updateOptions(params);

    // 设置线条宽度为1像素，实现像素化效果
    this.ctx.lineWidth = 1;

    // 使用插件化图案绘制，添加边距扩展参数确保铺满画布
    const margin = 80; // 边距扩展，确保图案铺满画布
    this.currentPattern.draw(this.ctx, this.data.canvasWidth, this.data.canvasHeight, margin);
  },

  // 绘制文字
  drawText() {
    const {
      textContent,
      fontSize,
      lineHeight,
      letterSpacing,
      textColor,
      isBold,
      isBlur,
      alignIndex,
      textX,
      textY
    } = this.data;

    if (!textContent.trim()) return;

    // 设置文字样式
    const fontWeight = isBold ? 'bold' : 'normal';
    // 应用外部字体
    this.ctx.font = `${fontWeight} ${fontSize}px 'Source Han Sans K', sans-serif`;
    this.ctx.fillStyle = textColor;

    // 处理模糊效果
    if (isBlur) {
      this.ctx.shadowBlur = 2;
      this.ctx.shadowColor = textColor;
    }

    // 分割文字行
    const lines = textContent.split('\n');
    const lineHeightPx = fontSize * lineHeight;

    // 垂直居中基线设置
    this.ctx.textBaseline = 'middle'; // 设置文字垂直居中

    // 绘制每一行文字
    lines.forEach((line, index) => {
      if (line.trim()) {
        const totalTextHeight = lines.length * lineHeightPx;
        const startY = textY - totalTextHeight / 2 + lineHeightPx / 2;
        const y = startY + index * lineHeightPx;

        // 统一调用 drawTextWithSpacing 来处理对齐和字间距
        this.drawTextWithSpacing(line, textX, y, letterSpacing, alignIndex); // 传递 alignIndex
      }
    });

    // 重置阴影
    if (isBlur) {
      this.ctx.shadowBlur = 0;
      this.ctx.shadowColor = 'transparent';
    }
  },

  // 绘制带字间距的文字，并处理对齐
  drawTextWithSpacing(text, x, y, spacing, alignIndex) { // 增加 alignIndex 参数
    const chars = text.split('');
    let currentX = x;

    // 确保 ctx.textAlign 始终为 'left'，因为我们要手动控制每个字符的绘制位置
    this.ctx.textAlign = 'left';

    // 计算总宽度
    let totalWidth = 0;
    if (spacing > 0) { // 只有在有字间距时才需要逐字符测量
      chars.forEach(char => {
        totalWidth += this.ctx.measureText(char).width;
      });
      totalWidth += (chars.length - 1) * spacing;
    } else { // 没有字间距时，直接测量整行文本的宽度
      totalWidth = this.ctx.measureText(text).width;
    }


    // 根据对齐方式调整起始位置
    if (alignIndex === 1) { // 居中
      currentX = x - totalWidth / 2;
    } else if (alignIndex === 2) { // 右对齐
      currentX = x - totalWidth;
    }
    // 左对齐 (alignIndex === 0) 时 currentX 保持为 x

    if (spacing > 0) {
      chars.forEach(char => {
        this.ctx.fillText(char, currentX, y);
        currentX += this.ctx.measureText(char).width + spacing;
      });
    } else {
      // 没有字间距时，直接绘制整行文本
      this.ctx.fillText(text, currentX, y);
    }
  },

  // 触摸事件处理
  onTouchStart(e) {
    const touch = e.touches[0];
    this.setData({
      isDragging: true,
      lastTouchX: touch.x,
      lastTouchY: touch.y
    });
  },

  onTouchMove(e) {
    if (!this.data.isDragging) return;

    const touch = e.touches[0];
    const deltaX = touch.x - this.data.lastTouchX;
    const deltaY = touch.y - this.data.lastTouchY;

    this.setData({
      textX: this.data.textX + deltaX,
      textY: this.data.textY + deltaY,
      lastTouchX: touch.x,
      lastTouchY: touch.y
    });

    this.drawCanvas();
  },

  onTouchEnd() {
    this.setData({
      isDragging: false
    });
  },

  // 文字设置事件处理
  onTextInput(e) {
    this.setData({
      textContent: e.detail.value
    });
    this.drawCanvas();
  },

  onFontSizeChange(e) {
    this.setData({
      fontSize: e.detail.value
    });
    this.drawCanvas();
  },

  onLineHeightChange(e) {
    this.setData({
      lineHeight: e.detail.value
    });
    this.drawCanvas();
  },

  onLetterSpacingChange(e) {
    this.setData({
      letterSpacing: e.detail.value
    });
    this.drawCanvas();
  },

  onAlignChange(e) {
    this.setData({
      alignIndex: e.detail.value
    });
    this.drawCanvas();
  },



  onBoldChange(e) {
    this.setData({
      isBold: e.detail.value
    });
    this.drawCanvas();
  },

  onBlurChange(e) {
    this.setData({
      isBlur: e.detail.value
    });
    this.drawCanvas();
  },

  // 干扰线设置事件处理
  onInterferenceToggle(e) {
    this.setData({
      enableInterference: e.detail.value
    });
    this.drawCanvas();
  },

  onPatternChange(e) {
    console.log("干扰线参数变化：", e)
    const patternIndex = e.detail.value;
    const selectedPattern = this.data.availablePatterns[patternIndex];

    // 初始化新图案的默认参数
    const defaultParams = {};
    selectedPattern.config.forEach(param => {
      if (param.type === 'picker') {
        // 对于picker类型，设置默认值和索引
        const defaultValue = param.values ? param.values[0] : param.options[0];
        defaultParams[param.key] = param.default || defaultValue;
        defaultParams[param.key + 'Index'] = 0;

        // 如果有预定义的默认值，找到对应的索引
        if (param.default) {
          const defaultIndex = param.values ?
            param.values.indexOf(param.default) :
            param.options.indexOf(param.default);
          if (defaultIndex !== -1) {
            defaultParams[param.key + 'Index'] = defaultIndex;
          }
        }
      } else if (param.type === 'color') {
        // 对于color类型，设置默认颜色值
        defaultParams[param.key] = param.default || '#000000';
      } else {
        defaultParams[param.key] = param.default;
      }
    });

    this.setData({
      patternIndex,
      currentPatternKey: selectedPattern.key,
      currentPatternConfig: selectedPattern.config,
      patternParams: defaultParams
    });

    this.updateCurrentPattern();
    this.drawCanvas();
  },

  // 动态参数变更处理
  onPatternParamChange(e) {
    console.log("参数变化:",e)
    const { key } = e.currentTarget.dataset;
    let value = e.detail.value;

    // 查找参数配置
    const paramConfig = this.data.currentPatternConfig.find(p => p.key === key);

    if (paramConfig) {
      if (paramConfig.type === 'picker') {
        // 对于picker类型，需要转换索引为实际值
        const actualValue = paramConfig.values ? paramConfig.values[value] : paramConfig.options[value];
        const updatedParams = {
          ...this.data.patternParams,
          [key]: actualValue,
          [key + 'Index']: value
        };

        this.setData({
          patternParams: updatedParams
        });
      } else if (paramConfig.type === 'switch') {
        // 对于switch类型，直接使用布尔值
        const updatedParams = {
          ...this.data.patternParams,
          [key]: value
        };

        this.setData({
          patternParams: updatedParams
        });
      } else if (paramConfig.type === 'color') {
        // 对于color类型，颜色值由onPatternColorChange处理，这里不需要处理
        return;
      } else {
        // 对于slider等其他类型，直接使用值
        const updatedParams = {
          ...this.data.patternParams,
          [key]: value
        };

        this.setData({
          patternParams: updatedParams
        });
      }
    }

    this.updateCurrentPattern();
    this.drawCanvas();
  },







  // 导出设置事件处理
  onFormatChange(e) {
    this.setData({
      formatIndex: e.detail.value
    });
  },



  onQualityChange(e) {
    this.setData({
      quality: e.detail.value
    });
  },

  // 预览功能
  onPreview() {
    this.drawCanvas();
    wx.showToast({
      title: '预览已更新',
      icon: 'success'
    });
  },

  // 导出图片
  onExport() {
    if (!this.canvas) {
      wx.showToast({
        title: '画布未准备好',
        icon: 'error'
      });
      return;
    }

    const { formatIndex, quality, canvasWidth, canvasHeight, pixelRatio } = this.data;
    const format = formatIndex === 0 ? 'png' : 'jpg';
    const destWidth = canvasWidth * pixelRatio;
    const destHeight = canvasHeight * pixelRatio;

    // 使用新的 Canvas 2D API 导出图片
    wx.canvasToTempFilePath({
      canvas: this.canvas,
      width: this.data.canvasWidth,
      height: this.data.canvasHeight,
      destWidth: destWidth,
      destHeight: destHeight,
      fileType: format,
      quality: quality,
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: (err) => {
            console.error('保存到相册失败:', err);
            wx.showModal({
              title: '保存失败',
              content: '请检查相册权限设置',
              showCancel: false
            });
          }
        });
      },
      fail: (err) => {
        console.error('导出失败:', err);
        wx.showToast({
          title: '导出失败',
          icon: 'error'
        });
      }
    });
  },

  // 颜色选择器事件处理
  onTextColorChange(e) {
    this.setData({
      textColor: e.detail.color
    });
    this.drawCanvas();
  },

  onBackgroundColorChange(e) {
    this.setData({
      backgroundColor: e.detail.color
    });
    this.drawCanvas();
  },

  onLineColorChange(e) {
    this.setData({
      lineColor: e.detail.color
    });
    this.drawCanvas();
  },

  onPatternColorChange(e) {
    const key = e.currentTarget.dataset.key;
    const color = e.detail.color;
    
    const updatedParams = {
      ...this.data.patternParams,
      [key]: color
    };

    this.setData({
      patternParams: updatedParams
    });

    this.updateCurrentPattern();
    this.drawCanvas();
  },

  onColorPickerToggle(e) {
    const type = e.currentTarget.dataset.type;
    const show = e.detail.show;
    
    // 如果当前颜色选择器要显示，关闭其他的
    if (show) {
      const query = this.createSelectorQuery();
      query.selectAll('color-picker').exec((res) => {
        if (res && res[0]) {
          res[0].forEach((component) => {
            // 关闭所有颜色选择器，当前的会在组件内部重新打开
            component.closePicker();
          });
        }
      });
    }
  },

  // 分享功能
  onShareAppMessage() {
    return {
      title: '隐藏文字生成器',
      path: '/pages/hidetext/hidetext?pattern=' + this.pattern,
      imageUrl: '' // 可以留空或添加分享图片URL
    };
  }
});