/* index.wxss - HiddenTextTool 样式 */
page {
  height: 100%;
  margin: 0;
  padding: 0;
  background-color: #f5f5f5;
  overflow: hidden;
}

.container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  justify-content: flex-start;
  background-color: #f5f5f5;
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  overflow: hidden;
  position: relative;
}

/* 画布区域 */
.canvas-container {
  width: 100%;
  height: auto;
  aspect-ratio: 1 / 1; /* 保持正方形 */
  background-color: #fff;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin: 0;
  padding: 0;
  flex-shrink: 0; /* 防止画布区域被压缩 */
  overflow: hidden;
}

.canvas {
  border: 1px solid #ddd;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  max-width: 100%;
  max-height: 100%;
  display: block;
  margin: 0 auto;
}

/* 控制面板 */
.control-panel {
  flex: 1;
  padding: 20rpx;
  background-color: #f5f5f5;
  height: calc(100vh - var(--canvas-size, 100vw) - var(--nav-height, 90rpx));
  overflow-y: auto;
  -webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
  box-sizing: border-box;
}

.section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 表单项 */
.form-item {
  margin-bottom: 30rpx;
}

.form-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.form-row .form-item {
  flex: 1;
  margin-bottom: 0;
  margin-right: 20rpx;
}

.form-row .form-item:last-child {
  margin-right: 0;
}

.label {
  display: block;
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
  font-weight: 500;
}

/* 输入框样式 */
.textarea {
  width: 100%;
  min-height: 120rpx;
  padding: 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  box-sizing: border-box;
}

.textarea:focus {
  border-color: #007aff;
  background-color: #fff;
}

/* 颜色选择器 */
.color-selector {
  display: flex;
  align-items: center;
  gap: 15rpx;
  width: 100%;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.color-preview {
  width: 80rpx;
  height: 60rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  flex-shrink: 0;
  box-shadow: inset 0 0 0 1rpx rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.2s ease;
}

.color-preview:active {
  transform: scale(0.95);
  border-color: #007aff;
}

.color-value {
  flex: 1;
  font-size: 22rpx;
  color: #666;
  font-family: monospace;
  background-color: #f8f8f8;
  padding: 12rpx 15rpx;
  border-radius: 6rpx;
  border: 1rpx solid #e0e0e0;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 颜色选择面板 */
.color-picker-panel {
  width: 100%;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-top: 20rpx;
  border: 1rpx solid #e0e0e0;
  box-sizing: border-box;
  overflow: hidden;
}

/* 预设颜色 */
.preset-colors {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 10rpx;
  margin-bottom: 30rpx;
  width: 100%;
  box-sizing: border-box;
}

.preset-color {
  width: 100%;
  aspect-ratio: 1;
  max-width: 45rpx;
  max-height: 45rpx;
  border-radius: 6rpx;
  border: 2rpx solid #e0e0e0;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
  box-sizing: border-box;
}

.preset-color:active {
  transform: scale(0.9);
  border-color: #007aff;
  box-shadow: 0 4rpx 8rpx rgba(0, 122, 255, 0.3);
}

/* 颜色滑块 */
.color-sliders {
  display: flex;
  flex-direction: column;
  gap: 25rpx;
}

.slider-item {
  display: flex;
  align-items: center;
  gap: 15rpx;
  width: 100%;
  box-sizing: border-box;
}

.slider-label {
  font-size: 22rpx;
  color: #666;
  width: 70rpx;
  flex-shrink: 0;
  text-align: right;
}

.slider-item slider {
  flex: 1;
  margin: 0;
  min-width: 0;
}

/* 动态参数表单样式 */
.dynamic-param-item {
  margin-bottom: 10rpx;
}

.dynamic-param-item .form-row:last-child {
  margin-bottom: 0;
}

.dynamic-param-item .label {
  font-weight: 500;
  color: #444;
}


/* 选择器样式 */
.picker {
  height: 60rpx;
  line-height: 60rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  background-color: #fafafa;
  color: #333;
}

/* 滑块样式 */
slider {
  margin: 20rpx 0;
}

/* 开关样式 */
switch {
  transform: scale(0.8);
}

/* 按钮组 */
.button-group {
  display: flex;
  justify-content: space-between;
  margin-top: 40rpx;
  gap: 20rpx;
}

.btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 500;
  border: none;
  color: #fff;
}

/* .btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.btn-primary:active {
  background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.btn-success {
  background: linear-gradient(135deg, #11998e 0%, #38ef7d 100%);
}

.btn-success:active {
  background: linear-gradient(135deg, #0e8078 0%, #32d96b 100%);
} */

/* 响应式调整 */
@media (max-width: 750rpx) {
  .form-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .form-row .form-item {
    margin-right: 0;
    margin-bottom: 20rpx;
  }
  
  .button-group {
    flex-direction: column;
  }
  
  .btn {
    margin-bottom: 20rpx;
  }
  
  .btn:last-child {
    margin-bottom: 0;
  }
  
  /* 小屏幕下的颜色选择器调整 */
  .preset-colors {
    grid-template-columns: repeat(5, 1fr);
    gap: 8rpx;
  }
  
  .color-picker-panel {
    padding: 15rpx;
  }
  
  .slider-label {
    width: 60rpx;
    font-size: 20rpx;
  }
  
  .slider-item {
    gap: 10rpx;
  }
}

/* 动画效果 */
.section {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6rpx;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3rpx;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}/* 自定义导航栏 */
.custom-nav {
  width: 100%;
  background-color: #1AAD19;
  display: flex;
  align-items: flex-end;
  justify-content: center;
  position: relative;
  padding-top: env(safe-area-inset-top);
  box-sizing: border-box;
  padding-bottom: 22rpx;
  z-index: 100;
  flex-shrink: 0; /* 防止导航栏被压缩 */
}

.nav-title {
  color: #ffffff;
  font-size: 36rpx;
  font-weight: 500;
  text-align: center;
}