.page-body {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input-section {
  width: 100%;
  margin-bottom: 30px;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.input {
  width: 80%;
  height: 40px;
  border: 1px solid #ccc;
  border-radius: 5px;
  padding: 0 10px;
  margin-bottom: 15px;
}

.generate-btn {
  width: 80%;
  background-color: #007aff;
  color: white;
  border: none;
  border-radius: 5px;
  height: 40px;
  font-size: 16px;
}

.qrcode-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 30px;
}

.qrcode-canvas {
  border: 1px solid #eee;
  margin-bottom: 15px;
}

.result-text {
  font-size: 14px;
  color: #666;
  margin-bottom: 15px;
}

.control-section {
  margin-bottom: 15px;
  display: flex;
  gap: 10px;
  flex-wrap: wrap;
  justify-content: center;
}

.toggle-btn {
  background-color: #f0f0f0;
  color: #333;
  border: 1px solid #ddd;
  border-radius: 5px;
  padding: 8px 16px;
  font-size: 14px;
  transition: all 0.3s;
}

.toggle-btn.active {
  background-color: #ff4444;
  color: white;
  border-color: #ff4444;
}

.legend-section {
  width: 100%;
  max-width: 300px;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 5px;
  border: 1px solid #eee;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.legend-item:last-child {
  margin-bottom: 0;
}

.legend-color {
  width: 16px;
  height: 16px;
  border-radius: 2px;
  margin-right: 8px;
  border: 1px solid #ccc;
}

.legend-color.black {
  background-color: #000000;
}

.legend-color.red {
  background-color: #ff0000;
}

.legend-color.red-overlay {
  background-color: rgba(255, 0, 0, 0.5);
  position: relative;
}

.legend-color.red-overlay::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #000000;
  z-index: -1;
}

.legend-color.white {
  background-color: #ffffff;
}

.legend-text {
  font-size: 12px;
  color: #666;
}

.note {
  margin-top: 10px;
  padding: 8px;
  background-color: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.note-text {
  font-size: 11px;
  color: #856404;
  line-height: 1.4;
}

/* 样式定制界面 */
.style-section {
  width: 100%;
  max-width: 400px;
  margin-top: 15px;
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa, #ffffff);
  border-radius: 12px;
  border: 1px solid #e9ecef;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.style-title {
  font-size: 16px;
  font-weight: bold;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.style-group {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

.style-group:last-child {
  margin-bottom: 0;
}

.style-group-title {
  font-size: 14px;
  font-weight: bold;
  color: #495057;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #e9ecef;
}

.style-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 10px;
  padding: 5px 0;
}

.style-item:last-child {
  margin-bottom: 0;
}

.style-label {
  font-size: 13px;
  color: #666;
  flex: 1;
}

.color-input {
  width: 45px;
  height: 35px;
  border: 2px solid #e9ecef;
  border-radius: 8px;
  padding: 0;
  transition: all 0.2s;
}

.color-input:focus {
  border-color: #007aff;
  box-shadow: 0 0 0 3px rgba(0, 122, 255, 0.1);
}

.picker {
  min-width: 85px;
  padding: 8px 12px;
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 6px;
  text-align: center;
  transition: all 0.2s;
}

.picker:active {
  background-color: #e9ecef;
  border-color: #007aff;
}

.picker-text {
  font-size: 12px;
  color: #333;
}

.switch {
  transform: scale(0.8);
}

.slider {
  width: 120px;
  margin: 0 10px;
}

/* 预设样式 */
.preset-section {
  margin-bottom: 20px;
  padding: 12px;
  background-color: #ffffff;
  border-radius: 6px;
  border: 1px solid #e9ecef;
}

.preset-title {
  font-size: 14px;
  font-weight: bold;
  color: #495057;
  margin-bottom: 10px;
  text-align: center;
}

.preset-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.preset-btn {
  flex: 1;
  min-width: 60px;
  padding: 8px 12px;
  font-size: 11px;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.preset-btn.classic {
  background: linear-gradient(135deg, #1a1a1a, #333333);
  color: white;
  border: 1px solid #333333;
}

.preset-btn.modern {
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border: 1px solid #667eea;
}

.preset-btn.colorful {
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
  color: white;
  border: 1px solid #ff6b6b;
}

.preset-btn.business {
  background: linear-gradient(135deg, #2d3748, #4a5568);
  color: white;
  border: 1px solid #2d3748;
}

.preset-btn.neon {
  background: linear-gradient(135deg, #0a0a0a, #1a1a1a);
  color: #00f5ff;
  border: 1px solid #00f5ff;
  box-shadow: 0 0 10px rgba(0, 245, 255, 0.3);
}

.preset-btn.nature {
  background: linear-gradient(135deg, #27ae60, #2ecc71);
  color: white;
  border: 1px solid #27ae60;
}

.preset-btn:active {
  transform: translateY(1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.info-section {
  width: 100%;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;
}

.info-text {
  font-size: 14px;
  color: #333;
  line-height: 1.5;
  margin-bottom: 10px;
}

.tips-text {
  font-size: 12px;
  color: #999;
  line-height: 1.4;
}