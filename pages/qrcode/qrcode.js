// index.js
const { QRCodeGenerator } = require('../../utils/qrcode.js')

Page({
  data: {
    motto: '二维码生成器',
    qrCodeText: '请输入信息',
    qrCodeResult: '',
    isShowCanvas: false,
    showMaskableAreas: false,
    showStyleOptions: false,
    styleOptions: {
      foregroundColor: '#000000',
      backgroundColor: '#ffffff',
      moduleShape: 'square', // square, circle, rounded
      gradientType: 'none', // none, linear, radial
      gradientColors: ['#000000', '#333333'],
      borderStyle: 'none', // none, solid, dashed
      borderColor: '#cccccc',
      logoEnabled: false,
      logoSize: 20 // percentage
    },
    // Picker选项数据
    shapeOptions: {
      labels: ['方形', '圆形', '圆角'],
      values: ['square', 'circle', 'rounded'],
      current: '方形'
    },
    gradientOptions: {
      labels: ['无', '线性', '径向'],
      values: ['none', 'linear', 'radial'],
      current: '无'
    },
    borderOptions: {
      labels: ['无', '实线', '虚线'],
      values: ['none', 'solid', 'dashed'],
      current: '无'
    }
  },

  onLoad() {
    // 初始化picker显示
    this.updateAllPickerDisplays()
  },

  onInputChange(e) {
    const value = e.detail.value
    this.setData({
      qrCodeText: value
    })
  },

  // 生成二维码
  generateQRCode() {
    const text = this.data.qrCodeText
    if (!text) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      })
      return
    }

    this.setData({
      qrCodeResult: text,
      isShowCanvas: true,
      showMaskableAreas: false
    })

    // 延迟绘制二维码，确保视图已更新
    setTimeout(() => {
      this.drawQRCode(text)
    }, 100)
  },

  // 切换显示可遮挡区域
  toggleMaskableAreas() {
    const showMaskable = !this.data.showMaskableAreas
    this.setData({
      showMaskableAreas: showMaskable,
      showStyleOptions: false // 关闭样式选项
    })

    if (this.data.qrCodeText) {
      setTimeout(() => {
        this.drawQRCode(this.data.qrCodeText)
      }, 100)
    }
  },

  // 切换样式选项
  toggleStyleOptions() {
    const showStyle = !this.data.showStyleOptions
    this.setData({
      showStyleOptions: showStyle,
      showMaskableAreas: false // 关闭可遮挡区域显示
    })

    if (this.data.qrCodeText) {
      setTimeout(() => {
        this.drawQRCode(this.data.qrCodeText)
      }, 100)
    }
  },

  // 更新样式选项
  updateStyleOption(e) {
    const { key, value } = e.currentTarget.dataset
    const styleOptions = { ...this.data.styleOptions }

    if (key.includes('.')) {
      // 处理嵌套属性，如 gradientColors.0
      const keys = key.split('.')
      if (keys[0] === 'gradientColors') {
        styleOptions.gradientColors[parseInt(keys[1])] = value
      }
    } else {
      styleOptions[key] = value
    }

    this.setData({ styleOptions })

    if (this.data.qrCodeText) {
      setTimeout(() => {
        this.drawQRCode(this.data.qrCodeText)
      }, 100)
    }
  },

  // 颜色选择器变化
  onColorChange(e) {
    const key = e.currentTarget.dataset.key
    const value = e.detail.value
    this.updateStyleOption({ currentTarget: { dataset: { key, value } } })
  },

  // 选择器变化
  onPickerChange(e) {
    const key = e.currentTarget.dataset.key
    const options = e.currentTarget.dataset.options
    const selectedIndex = e.detail.value
    const value = options[selectedIndex]

    // 更新样式选项
    this.updateStyleOption({ currentTarget: { dataset: { key, value } } })

    // 更新picker显示文本
    this.updatePickerDisplay(key, selectedIndex)
  },

  // 更新picker显示文本
  updatePickerDisplay(key, selectedIndex) {
    const updates = {}

    switch (key) {
      case 'moduleShape':
        updates['shapeOptions.current'] = this.data.shapeOptions.labels[selectedIndex]
        break
      case 'gradientType':
        updates['gradientOptions.current'] = this.data.gradientOptions.labels[selectedIndex]
        break
      case 'borderStyle':
        updates['borderOptions.current'] = this.data.borderOptions.labels[selectedIndex]
        break
    }

    if (Object.keys(updates).length > 0) {
      this.setData(updates)
    }
  },

  // 滑块变化
  onSliderChange(e) {
    const key = e.currentTarget.dataset.key
    const value = e.detail.value
    this.updateStyleOption({ currentTarget: { dataset: { key, value } } })
  },

  // 开关变化
  onSwitchChange(e) {
    const key = e.currentTarget.dataset.key
    const value = e.detail.value
    this.updateStyleOption({ currentTarget: { dataset: { key, value } } })
  },

  // 应用预设样式
  applyPresetStyle(e) {
    const preset = e.currentTarget.dataset.preset
    let styleOptions = { ...this.data.styleOptions }

    switch (preset) {
      case 'classic':
        // 经典黑白 - 最佳识别性
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#000000',
          backgroundColor: '#ffffff',
          moduleShape: 'square',
          gradientType: 'none',
          borderStyle: 'none',
          logoEnabled: false
        }
        break
      case 'modern':
        // 现代蓝色 - 保持高对比度
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#1e40af',
          backgroundColor: '#ffffff',
          moduleShape: 'rounded',
          gradientType: 'none',
          borderStyle: 'none',
          logoEnabled: true,
          logoSize: 18
        }
        break
      case 'colorful':
        // 活力红色 - 单色保证识别
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#dc2626',
          backgroundColor: '#ffffff',
          moduleShape: 'circle',
          gradientType: 'none',
          borderStyle: 'none',
          logoEnabled: true,
          logoSize: 20
        }
        break
      case 'business':
        // 商务深灰 - 专业稳重
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#374151',
          backgroundColor: '#ffffff',
          moduleShape: 'square',
          gradientType: 'none',
          borderStyle: 'solid',
          borderColor: '#d1d5db',
          logoEnabled: true,
          logoSize: 16
        }
        break
      case 'neon':
        // 科技紫色 - 现代感
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#7c3aed',
          backgroundColor: '#ffffff',
          moduleShape: 'rounded',
          gradientType: 'none',
          borderStyle: 'none',
          logoEnabled: true,
          logoSize: 20
        }
        break
      case 'nature':
        // 自然绿色 - 环保主题
        styleOptions = {
          ...styleOptions,
          foregroundColor: '#059669',
          backgroundColor: '#ffffff',
          moduleShape: 'rounded',
          gradientType: 'none',
          borderStyle: 'none',
          logoEnabled: true,
          logoSize: 18
        }
        break
    }

    this.setData({ styleOptions })

    // 更新picker显示
    this.updateAllPickerDisplays()

    if (this.data.qrCodeText) {
      setTimeout(() => {
        this.drawQRCode(this.data.qrCodeText)
      }, 100)
    }
  },

  // 更新所有picker显示
  updateAllPickerDisplays() {
    const { styleOptions, shapeOptions, gradientOptions, borderOptions } = this.data

    // 找到对应的索引并更新显示
    const shapeIndex = shapeOptions.values.indexOf(styleOptions.moduleShape)
    const gradientIndex = gradientOptions.values.indexOf(styleOptions.gradientType)
    const borderIndex = borderOptions.values.indexOf(styleOptions.borderStyle)

    this.setData({
      'shapeOptions.current': shapeOptions.labels[shapeIndex] || '方形',
      'gradientOptions.current': gradientOptions.labels[gradientIndex] || '无',
      'borderOptions.current': borderOptions.labels[borderIndex] || '无'
    })
  },

  // 绘制二维码
  drawQRCode(text) {
    try {
      // 创建QR码生成器实例
      const qrGenerator = new QRCodeGenerator()

      let qrMatrix, maskableMatrix

      if (this.data.showMaskableAreas) {
        // 生成包含可遮挡区域信息的矩阵
        const result = qrGenerator.generateWithMaskableAreas(text)
        qrMatrix = result.qrMatrix
        maskableMatrix = result.maskableMatrix
        console.log('版本:', result.version, '大小:', result.moduleCount + 'x' + result.moduleCount)
      } else {
        // 生成普通QR码矩阵
        qrMatrix = qrGenerator.generate(text)
      }

      const qrcodeSize = 200
      const canvas = wx.createCanvasContext('qrcodeCanvas', this)
      const cellSize = Math.floor(qrcodeSize / qrMatrix.length)
      const actualSize = cellSize * qrMatrix.length

      // 清空画布
      canvas.clearRect(0, 0, qrcodeSize, qrcodeSize)

      // 绘制白色背景
      canvas.setFillStyle('#ffffff')
      canvas.fillRect(0, 0, qrcodeSize, qrcodeSize)

      // 计算居中偏移
      const offsetX = (qrcodeSize - actualSize) / 2
      const offsetY = (qrcodeSize - actualSize) / 2

      if (this.data.showMaskableAreas && maskableMatrix) {
        // 可遮挡区域显示模式
        this.drawMaskableAreasMode(canvas, qrMatrix, maskableMatrix, offsetX, offsetY, cellSize)
      } else {
        // 普通模式或样式定制模式
        this.drawStyledQRCode(canvas, qrMatrix, offsetX, offsetY, cellSize, qrcodeSize)
      }

      canvas.draw(false, () => {
        const message = this.data.showMaskableAreas ?
          '红色覆盖区域可遮挡，二维码仍可识别' :
          '二维码生成成功'
        console.log('二维码生成成功，矩阵大小:', qrMatrix.length + 'x' + qrMatrix.length)
        wx.showToast({
          title: message,
          icon: this.data.showMaskableAreas ? 'none' : 'success'
        })
      })
    } catch (error) {
      console.error('二维码生成失败:', error)
      wx.showToast({
        title: '生成失败: ' + error.message,
        icon: 'none'
      })
    }
  },

  // 绘制可遮挡区域模式
  drawMaskableAreasMode(canvas, qrMatrix, maskableMatrix, offsetX, offsetY, cellSize) {
    // 第一步：绘制标准二维码
    canvas.setFillStyle('#000000')
    for (let row = 0; row < qrMatrix.length; row++) {
      for (let col = 0; col < qrMatrix[row].length; col++) {
        if (qrMatrix[row][col] === 1) {
          const x = offsetX + col * cellSize
          const y = offsetY + row * cellSize
          canvas.fillRect(x, y, cellSize, cellSize)
        }
      }
    }

    // 第二步：用半透明红色覆盖可遮挡区域
    canvas.setGlobalAlpha(0.5)
    canvas.setFillStyle('#ff0000')
    for (let row = 0; row < qrMatrix.length; row++) {
      for (let col = 0; col < qrMatrix[row].length; col++) {
        if (maskableMatrix[row][col]) {
          const x = offsetX + col * cellSize
          const y = offsetY + row * cellSize
          canvas.fillRect(x, y, cellSize, cellSize)
        }
      }
    }
    canvas.setGlobalAlpha(1.0)
  },

  // 绘制样式化二维码
  drawStyledQRCode(canvas, qrMatrix, offsetX, offsetY, cellSize, canvasSize) {
    const style = this.data.styleOptions

    // 绘制背景
    this.drawBackground(canvas, canvasSize, style)

    // 绘制二维码模块
    for (let row = 0; row < qrMatrix.length; row++) {
      for (let col = 0; col < qrMatrix[row].length; col++) {
        if (qrMatrix[row][col] === 1) {
          const x = offsetX + col * cellSize
          const y = offsetY + row * cellSize
          this.drawModule(canvas, x, y, cellSize, style, row, col, qrMatrix.length)
        }
      }
    }

    // 绘制Logo（如果启用）
    if (style.logoEnabled) {
      this.drawLogo(canvas, offsetX, offsetY, qrMatrix.length * cellSize, style)
    }

    // 绘制边框
    if (style.borderStyle !== 'none') {
      this.drawBorder(canvas, offsetX, offsetY, qrMatrix.length * cellSize, style)
    }
  },

  // 绘制背景
  drawBackground(canvas, canvasSize, style) {
    if (style.gradientType === 'linear') {
      const gradient = canvas.createLinearGradient(0, 0, canvasSize, canvasSize)
      gradient.addColorStop(0, style.backgroundColor)
      gradient.addColorStop(1, style.gradientColors[1] || style.backgroundColor)
      canvas.setFillStyle(gradient)
    } else if (style.gradientType === 'radial') {
      const gradient = canvas.createCircularGradient(canvasSize / 2, canvasSize / 2, 0, canvasSize / 2, canvasSize / 2, canvasSize / 2)
      gradient.addColorStop(0, style.backgroundColor)
      gradient.addColorStop(1, style.gradientColors[1] || style.backgroundColor)
      canvas.setFillStyle(gradient)
    } else {
      canvas.setFillStyle(style.backgroundColor)
    }
    canvas.fillRect(0, 0, canvasSize, canvasSize)
  },

  // 绘制单个模块
  drawModule(canvas, x, y, size, style, row, col, matrixSize) {
    // 设置颜色 - 简化为单色，确保识别性
    if (style.gradientType === 'linear') {
      const gradient = canvas.createLinearGradient(x, y, x + size, y + size)
      gradient.addColorStop(0, style.foregroundColor)
      gradient.addColorStop(1, style.gradientColors[0] || style.foregroundColor)
      canvas.setFillStyle(gradient)
    } else if (style.gradientType === 'radial') {
      const gradient = canvas.createCircularGradient(x + size / 2, y + size / 2, 0, x + size / 2, y + size / 2, size / 2)
      gradient.addColorStop(0, style.foregroundColor)
      gradient.addColorStop(1, style.gradientColors[0] || style.foregroundColor)
      canvas.setFillStyle(gradient)
    } else {
      canvas.setFillStyle(style.foregroundColor)
    }

    // 绘制不同形状 - 保持标准尺寸
    switch (style.moduleShape) {
      case 'circle':
        canvas.beginPath()
        canvas.arc(x + size / 2, y + size / 2, size / 2 * 0.8, 0, 2 * Math.PI)
        canvas.fill()
        break
      case 'rounded':
        this.drawRoundedRect(canvas, x, y, size, size, size * 0.2)
        break
      default: // square
        canvas.fillRect(x, y, size, size)
        break
    }
  },

  // 调整颜色亮度
  adjustColorBrightness(color, percent) {
    const num = parseInt(color.replace("#", ""), 16)
    const amt = Math.round(2.55 * percent)
    const R = (num >> 16) + amt
    const G = (num >> 8 & 0x00FF) + amt
    const B = (num & 0x0000FF) + amt
    return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
      (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
      (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
  },

  // 绘制圆角矩形
  drawRoundedRect(canvas, x, y, width, height, radius) {
    canvas.beginPath()
    canvas.moveTo(x + radius, y)
    canvas.lineTo(x + width - radius, y)
    canvas.quadraticCurveTo(x + width, y, x + width, y + radius)
    canvas.lineTo(x + width, y + height - radius)
    canvas.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    canvas.lineTo(x + radius, y + height)
    canvas.quadraticCurveTo(x, y + height, x, y + height - radius)
    canvas.lineTo(x, y + radius)
    canvas.quadraticCurveTo(x, y, x + radius, y)
    canvas.fill()
  },

  // 绘制Logo
  drawLogo(canvas, offsetX, offsetY, qrSize, style) {
    const logoSize = qrSize * (style.logoSize / 100)
    const logoX = offsetX + (qrSize - logoSize) / 2
    const logoY = offsetY + (qrSize - logoSize) / 2
    const padding = logoSize * 0.15

    // 绘制Logo阴影
    canvas.setShadow(2, 2, 8, 'rgba(0, 0, 0, 0.15)')

    // 绘制Logo背景
    const bgGradient = canvas.createLinearGradient(logoX - padding, logoY - padding, logoX + logoSize + padding, logoY + logoSize + padding)
    bgGradient.addColorStop(0, '#ffffff')
    bgGradient.addColorStop(1, '#f8f9fa')
    canvas.setFillStyle(bgGradient)
    this.drawRoundedRect(canvas, logoX - padding, logoY - padding, logoSize + padding * 2, logoSize + padding * 2, logoSize * 0.2)

    // 清除阴影
    canvas.setShadow(0, 0, 0, 'transparent')

    // 绘制Logo边框
    canvas.setStrokeStyle(style.foregroundColor)
    canvas.setLineWidth(1.5)
    canvas.beginPath()
    this.drawRoundedRectPath(canvas, logoX - padding, logoY - padding, logoSize + padding * 2, logoSize + padding * 2, logoSize * 0.2)
    canvas.stroke()

    // 绘制Logo图标 (使用几何图形代替文字)
    const iconSize = logoSize * 0.4
    const iconX = logoX + logoSize / 2 - iconSize / 2
    const iconY = logoY + logoSize / 2 - iconSize / 2

    // 创建Logo图标渐变
    const iconGradient = canvas.createLinearGradient(iconX, iconY, iconX + iconSize, iconY + iconSize)
    iconGradient.addColorStop(0, style.foregroundColor)
    iconGradient.addColorStop(1, this.adjustColorBrightness(style.foregroundColor, -20))
    canvas.setFillStyle(iconGradient)

    // 绘制简单的几何Logo
    canvas.beginPath()
    canvas.arc(logoX + logoSize / 2, logoY + logoSize / 2, iconSize / 2, 0, 2 * Math.PI)
    canvas.fill()

    // 添加内部装饰
    canvas.setFillStyle('#ffffff')
    canvas.beginPath()
    canvas.arc(logoX + logoSize / 2, logoY + logoSize / 2, iconSize / 4, 0, 2 * Math.PI)
    canvas.fill()
  },

  // 绘制圆角矩形路径（用于描边）
  drawRoundedRectPath(canvas, x, y, width, height, radius) {
    canvas.moveTo(x + radius, y)
    canvas.lineTo(x + width - radius, y)
    canvas.quadraticCurveTo(x + width, y, x + width, y + radius)
    canvas.lineTo(x + width, y + height - radius)
    canvas.quadraticCurveTo(x + width, y + height, x + width - radius, y + height)
    canvas.lineTo(x + radius, y + height)
    canvas.quadraticCurveTo(x, y + height, x, y + height - radius)
    canvas.lineTo(x, y + radius)
    canvas.quadraticCurveTo(x, y, x + radius, y)
  },

  // 绘制边框
  drawBorder(canvas, offsetX, offsetY, qrSize, style) {
    canvas.setStrokeStyle(style.borderColor)
    canvas.setLineWidth(2)

    if (style.borderStyle === 'dashed') {
      // 微信小程序中手动绘制虚线边框
      this.drawDashedRect(canvas, offsetX - 5, offsetY - 5, qrSize + 10, qrSize + 10)
    } else {
      // 实线边框
      canvas.beginPath()
      canvas.rect(offsetX - 5, offsetY - 5, qrSize + 10, qrSize + 10)
      canvas.stroke()
    }
  },

  // 绘制虚线矩形
  drawDashedRect(canvas, x, y, width, height) {
    const dashLength = 5
    const gapLength = 5

    canvas.beginPath()

    // 上边
    this.drawDashedLine(canvas, x, y, x + width, y, dashLength, gapLength)
    // 右边
    this.drawDashedLine(canvas, x + width, y, x + width, y + height, dashLength, gapLength)
    // 下边
    this.drawDashedLine(canvas, x + width, y + height, x, y + height, dashLength, gapLength)
    // 左边
    this.drawDashedLine(canvas, x, y + height, x, y, dashLength, gapLength)

    canvas.stroke()
  },

  // 绘制虚线
  drawDashedLine(canvas, x1, y1, x2, y2, dashLength, gapLength) {
    const dx = x2 - x1
    const dy = y2 - y1
    const distance = Math.sqrt(dx * dx + dy * dy)
    const unitX = dx / distance
    const unitY = dy / distance

    let currentDistance = 0
    let isDash = true

    while (currentDistance < distance) {
      const segmentLength = isDash ? dashLength : gapLength
      const endDistance = Math.min(currentDistance + segmentLength, distance)

      const startX = x1 + currentDistance * unitX
      const startY = y1 + currentDistance * unitY
      const endX = x1 + endDistance * unitX
      const endY = y1 + endDistance * unitY

      if (isDash) {
        canvas.moveTo(startX, startY)
        canvas.lineTo(endX, endY)
      }

      currentDistance = endDistance
      isDash = !isDash
    }
  },
})