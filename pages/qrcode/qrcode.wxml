<view class="container">
  <view class="page-body">
    <view class="input-section">
      <input 
        class="input" 
        placeholder="请输入要生成二维码的内容" 
        value="{{qrCodeText}}" 
        bindinput="onInputChange"
      />
      <button class="generate-btn" bindtap="generateQRCode">生成二维码</button>
    </view>

    <view class="qrcode-section" wx:if="{{isShowCanvas}}">
      <canvas 
        class="qrcode-canvas" 
        canvas-id="qrcodeCanvas" 
        style="width: 200px; height: 200px;">
      </canvas>
      <text class="result-text">内容: {{qrCodeResult}}</text>
      
      <view class="control-section">
        <button 
          class="toggle-btn {{showMaskableAreas ? 'active' : ''}}" 
          bindtap="toggleMaskableAreas">
          {{showMaskableAreas ? '显示普通二维码' : '显示可遮挡区域'}}
        </button>
        <button 
          class="toggle-btn {{showStyleOptions ? 'active' : ''}}" 
          bindtap="toggleStyleOptions">
          {{showStyleOptions ? '关闭样式定制' : '样式定制'}}
        </button>
      </view>
      
      <!-- 可遮挡区域图例 -->
      <view class="legend-section" wx:if="{{showMaskableAreas}}">
        <view class="legend-item">
          <view class="legend-color black"></view>
          <text class="legend-text">不可遮挡区域（功能图案）</text>
        </view>
        <view class="legend-item">
          <view class="legend-color red-overlay"></view>
          <text class="legend-text">可遮挡区域（半透明红色覆盖）</text>
        </view>
        <view class="legend-item">
          <view class="legend-color white"></view>
          <text class="legend-text">空白区域</text>
        </view>
        <view class="note">
          <text class="note-text">💡 红色覆盖区域表示可以安全遮挡的数据区域，二维码仍可正常识别</text>
        </view>
      </view>

      <!-- 样式定制选项 -->
      <view class="style-section" wx:if="{{showStyleOptions}}">
        <view class="style-title">🎨 样式定制</view>
        
        <!-- 预设样式 -->
        <view class="preset-section">
          <view class="preset-title">快速预设</view>
          <view class="preset-buttons">
            <button class="preset-btn classic" data-preset="classic" bindtap="applyPresetStyle">经典</button>
            <button class="preset-btn modern" data-preset="modern" bindtap="applyPresetStyle">现代</button>
            <button class="preset-btn colorful" data-preset="colorful" bindtap="applyPresetStyle">彩色</button>
            <button class="preset-btn business" data-preset="business" bindtap="applyPresetStyle">商务</button>
            <button class="preset-btn neon" data-preset="neon" bindtap="applyPresetStyle">霓虹</button>
            <button class="preset-btn nature" data-preset="nature" bindtap="applyPresetStyle">自然</button>
          </view>
        </view>
        
        <!-- 颜色设置 -->
        <view class="style-group">
          <view class="style-group-title">颜色设置</view>
          <view class="style-item">
            <text class="style-label">前景色</text>
            <input type="color" value="{{styleOptions.foregroundColor}}" data-key="foregroundColor" bindchange="onColorChange" class="color-input" />
          </view>
          <view class="style-item">
            <text class="style-label">背景色</text>
            <input type="color" value="{{styleOptions.backgroundColor}}" data-key="backgroundColor" bindchange="onColorChange" class="color-input" />
          </view>
        </view>

        <!-- 形状设置 -->
        <view class="style-group">
          <view class="style-group-title">模块形状</view>
          <view class="style-item">
            <text class="style-label">形状</text>
            <picker range="{{shapeOptions.labels}}" data-key="moduleShape" data-options="{{shapeOptions.values}}" bindchange="onPickerChange" class="picker">
              <view class="picker-text">{{shapeOptions.current}}</view>
            </picker>
          </view>
        </view>

        <!-- 渐变设置 -->
        <view class="style-group">
          <view class="style-group-title">渐变效果</view>
          <view class="style-item">
            <text class="style-label">渐变类型</text>
            <picker range="{{gradientOptions.labels}}" data-key="gradientType" data-options="{{gradientOptions.values}}" bindchange="onPickerChange" class="picker">
              <view class="picker-text">{{gradientOptions.current}}</view>
            </picker>
          </view>
          <view class="style-item" wx:if="{{styleOptions.gradientType !== 'none'}}">
            <text class="style-label">渐变色</text>
            <input type="color" value="{{styleOptions.gradientColors[0]}}" data-key="gradientColors.0" bindchange="onColorChange" class="color-input" />
          </view>
        </view>

        <!-- Logo设置 -->
        <view class="style-group">
          <view class="style-group-title">Logo设置</view>
          <view class="style-item">
            <text class="style-label">启用Logo</text>
            <switch checked="{{styleOptions.logoEnabled}}" data-key="logoEnabled" bindchange="onSwitchChange" class="switch" />
          </view>
          <view class="style-item" wx:if="{{styleOptions.logoEnabled}}">
            <text class="style-label">Logo大小 ({{styleOptions.logoSize}}%)</text>
            <slider value="{{styleOptions.logoSize}}" min="10" max="30" data-key="logoSize" bindchange="onSliderChange" class="slider" />
          </view>
        </view>

        <!-- 边框设置 -->
        <view class="style-group">
          <view class="style-group-title">边框设置</view>
          <view class="style-item">
            <text class="style-label">边框样式</text>
            <picker range="{{borderOptions.labels}}" data-key="borderStyle" data-options="{{borderOptions.values}}" bindchange="onPickerChange" class="picker">
              <view class="picker-text">{{borderOptions.current}}</view>
            </picker>
          </view>
          <view class="style-item" wx:if="{{styleOptions.borderStyle !== 'none'}}">
            <text class="style-label">边框颜色</text>
            <input type="color" value="{{styleOptions.borderColor}}" data-key="borderColor" bindchange="onColorChange" class="color-input" />
          </view>
        </view>
      </view>
    </view>

    <view class="info-section">
      <text class="info-text">本小程序实现标准QR码生成，支持字节模式编码，包含纠错功能，生成的二维码可被标准扫描器识别。</text>
      <text class="tips-text">提示：建议输入较短的文本以确保最佳识别效果</text>
    </view>
  </view>
</view>