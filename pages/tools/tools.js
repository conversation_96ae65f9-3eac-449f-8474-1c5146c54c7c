// pages/index/index.js
Page({

  /**
   * 页面的初始数据
   */
  data: {

  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 打开全屏弹幕小程序
   */
  openDanmuApp() {
    wx.navigateToMiniProgram({
      appId: 'wx90b634ac373c6a1f',
      path: '', // 可以指定具体页面路径，留空则打开首页
      success: () => {
        console.log('成功打开全屏弹幕小程序');
      },
      fail: (err) => {
        console.error('打开小程序失败:', err);
        wx.showToast({
          title: '打开失败，请稍后重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '隐藏文字生成器',
      path: '/pages/tools/tools',
      imageUrl: '' // 可以留空或添加分享图片URL
    };
  }
})