/* pages/anti_folding/anti_folding.wxss */
.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
  box-sizing: border-box;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  border-radius: 20rpx;
  color: white;
  box-sizing: border-box;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  opacity: 0.9;
}

.info-section {
  margin-bottom: 30rpx;
}

.info-card {
  background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
  border-radius: 20rpx;
  padding: 30rpx;
  color: white;
  text-align: center;
  box-shadow: 0 4rpx 20rpx rgba(7, 193, 96, 0.2);
}

.info-title {
  font-size: 36rpx;
  font-weight: bold;
  display: block;
  margin-bottom: 15rpx;
}

.info-desc {
  font-size: 28rpx;
  line-height: 1.5;
  opacity: 0.9;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.input-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  width: 100%;
}

.input-textarea {
  width: 100%;
  min-height: 300rpx;
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 20rpx;
  font-size: 28rpx;
  line-height: 1.6;
  margin-top: 20rpx;
  width: 100%;
  transition: border-color 0.3s;
}

.input-textarea:focus {
  border-color: #07c160;
}

.char-count {
  text-align: right;
  font-size: 24rpx;
  color: #999;
  margin-top: 10rpx;
}

.button-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.convert-btn {
  flex: 2;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 32rpx;
  font-weight: bold;
  background-color: #07c160 !important;
  border: none;
}

.clear-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  background-color: #f0f0f0;
  color: #666;
}

.output-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  width: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

.copy-btn {
  background-color: #07c160;
  color: white;
  border-radius: 20rpx;
}

.output-container {
  border: 2rpx solid #e0e0e0;
  border-radius: 15rpx;
  padding: 20rpx;
  background-color: #fafafa;
}

.output-text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333;
  word-break: break-all;
}

.tips-section {
  background: white;
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.1);
  box-sizing: border-box;
  width: 100%;
}

.tips-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  display: block;
  margin-bottom: 20rpx;
}

.tips-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.tip-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  padding: 10rpx 0;
  border-left: 4rpx solid #07c160;
  padding-left: 20rpx;
  margin-left: 10rpx;
}