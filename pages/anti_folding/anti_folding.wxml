<!--pages/anti_folding/anti_folding.wxml-->
<view class="container">
  <!-- 功能说明 -->
  <view class="info-section">
    <view class="info-card">
      <text class="info-title">✨ 智能防折叠</text>
      <text class="info-desc">自动在文本中插入不可见字符，防止朋友圈折叠，不影响阅读体验</text>
    </view>
  </view>

  <!-- 输入区域 -->
  <view class="input-section">
    <text class="section-title">输入文本</text>
    <textarea 
      class="input-textarea"
      placeholder="请输入要转换的文本内容..."
      value="{{inputText}}"
      bindinput="onInputChange"
      maxlength="2000"
      show-confirm-bar="{{false}}"
    />
    <view class="char-count">{{inputText.length}}/2000</view>
  </view>

  <!-- 操作按钮 -->
  <view class="button-section">
    <button class="convert-btn" bindtap="convertText" type="primary" loading="{{isConverting}}">
      {{isConverting ? '转换中...' : '一键转换'}}
    </button>
    <button class="clear-btn" bindtap="clearAll">
      清空
    </button>
  </view>

  <!-- 输出区域 -->
  <view class="output-section" wx:if="{{outputText}}">
    <view class="section-header">
      <text class="section-title">转换结果</text>
      <button class="copy-btn" bindtap="copyResult" size="mini">
        复制
      </button>
    </view>
    <view class="output-container">
      <text class="output-text" selectable="true">{{outputText}}</text>
    </view>
  </view>

  <!-- 使用说明 -->
  <view class="tips-section">
    <text class="tips-title">💡 使用说明</text>
    <view class="tips-list">
      <text class="tip-item">1. 输入你要发布的文字内容</text>
      <text class="tip-item">2. 点击"一键转换"按钮</text>
      <text class="tip-item">3. 复制转换结果到朋友圈发布</text>
      <text class="tip-item">4. 文字将完整展示，不会被折叠</text>
    </view>
  </view>
</view>