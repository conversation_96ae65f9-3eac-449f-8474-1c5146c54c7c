// pages/anti_folding/anti_folding.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    inputText: '',
    outputText: '',
    isConverting: false
  },

  // 输入文本变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 智能转换文本（自动选择最佳方法）
  convertText() {
    const { inputText } = this.data;
    if (!inputText.trim()) {
      wx.showToast({
        title: '请输入要转换的文本',
        icon: 'none'
      });
      return;
    }

    this.setData({ isConverting: true });

    // 使用不可见字符方法，效果最好且不影响阅读
    const outputText = this.smartConvert(inputText);

    setTimeout(() => {
      this.setData({
        outputText,
        isConverting: false
      });
    }, 500);
  },

  // 智能转换算法
  smartConvert(text) {
    // 使用零宽空格，在关键位置插入
    const invisibleChar = '\u200B';
    
    // 在每个句子的中间位置插入不可见字符
    let result = text.replace(/(.{10})/g, `$1${invisibleChar}`);
    
    // 在标点符号后插入
    result = result.replace(/([。！？；，])/g, `$1${invisibleChar}`);
    
    // 在换行符前后插入
    result = result.replace(/\n/g, `${invisibleChar}\n${invisibleChar}`);
    
    return result;
  },

  // 复制结果
  copyResult() {
    const { outputText } = this.data;
    if (!outputText) {
      wx.showToast({
        title: '没有可复制的内容',
        icon: 'none'
      });
      return;
    }

    wx.setClipboardData({
      data: outputText,
      success: () => {
        wx.showToast({
          title: '复制成功',
          icon: 'success'
        });
      }
    });
  },

  // 清空内容
  clearAll() {
    this.setData({
      inputText: '',
      outputText: ''
    });
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {

  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  }
})