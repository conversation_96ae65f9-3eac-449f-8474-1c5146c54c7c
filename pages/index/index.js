// pages/hidepatterns/hidepatterns.js
const PatternManager = require('../../utils/PatternManager');

Page({

  /**
   * 页面的初始数据
   */
  data: {
    patterns: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.initPatterns();
  },

  // 初始化图案列表
  initPatterns() {
    const patternManager = new PatternManager();
    const patternsInfo = patternManager.getAllPatternsInfo();
    
    this.setData({
      patterns: patternsInfo
    });
  },

  // 点击图案项，跳转到hidetext页面
  onPatternTap(e) {
    const { key } = e.currentTarget.dataset;
    wx.navigateTo({
      url: `/pages/hidetext/hidetext?pattern=${key}`
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {
    return {
      title: '隐藏文字生成器',
      path: '/pages/index/index',
      imageUrl: '' // 可以留空或添加分享图片URL
    };
  }
})