/* pages/hidepatterns/hidepatterns.wxss */
.container {
  padding: 40rpx 30rpx;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 60rpx;
}

.title {
  display: block;
  font-size: 48rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
}

.subtitle {
  display: block;
  font-size: 28rpx;
  color: #666;
}

.pattern-list {
  background-color: #fff;
  border-radius: 20rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  width: 100%;
}

.pattern-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  transition: all 0.2s ease;
  position: relative;
}

.pattern-item:last-child {
  border-bottom: none;
}

.pattern-item:active {
  background-color: #f5f5f5;
  transform: scale(0.98);
}

.pattern-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  background-color: #f0f8ff;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 30rpx;
}

.icon {
  font-size: 36rpx;
}

.pattern-info {
  flex: 1;
}

.pattern-name {
  display: block;
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
}

.pattern-desc {
  display: block;
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.pattern-arrow {
  margin-left: 20rpx;
}

.arrow {
  font-size: 32rpx;
  color: #07c160;
  font-weight: bold;
}