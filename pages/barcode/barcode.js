// pages/barcode/barcode.js
Page({
  data: {
    inputText: '',
    typeIndex: 0,
    barcodeWidth: 200,
    barcodeHeight: 100,
    showBarcode: false,
    barcodeTypes: [
      { name: 'Code128', value: 'code128' },
      { name: 'Code39', value: 'code39' },
      { name: 'EAN13', value: 'ean13' },
      { name: 'EAN8', value: 'ean8' },
      { name: 'UPC', value: 'upc' }
    ],
    historyList: []
  },

  onLoad(options) {
    this.loadHistoryData();
  },

  onReady() {
    this.ctx = wx.createCanvasContext('barcodeCanvas');
  },

  // 输入内容变化
  onInputChange(e) {
    this.setData({
      inputText: e.detail.value
    });
  },

  // 条码类型变化
  onTypeChange(e) {
    this.setData({
      typeIndex: parseInt(e.detail.value)
    });
  },

  // 尺寸变化
  onSizeChange(e) {
    const width = e.detail.value;
    this.setData({
      barcodeWidth: width,
      barcodeHeight: Math.floor(width * 0.5)
    });
  },

  // 生成条码
  generateBarcode() {
    const { inputText, typeIndex, barcodeTypes } = this.data;
    
    if (!inputText.trim()) {
      wx.showToast({
        title: '请输入内容',
        icon: 'none'
      });
      return;
    }

    // 验证输入内容
    if (!this.validateInput(inputText, barcodeTypes[typeIndex].value)) {
      return;
    }

    this.drawBarcode();
    this.saveToHistory();
    
    this.setData({
      showBarcode: true
    });
  },

  // 验证输入内容
  validateInput(text, type) {
    switch (type) {
      case 'ean13':
        if (!/^\d{12,13}$/.test(text)) {
          wx.showToast({
            title: 'EAN13需要12-13位数字',
            icon: 'none'
          });
          return false;
        }
        break;
      case 'ean8':
        if (!/^\d{7,8}$/.test(text)) {
          wx.showToast({
            title: 'EAN8需要7-8位数字',
            icon: 'none'
          });
          return false;
        }
        break;
      case 'upc':
        if (!/^\d{11,12}$/.test(text)) {
          wx.showToast({
            title: 'UPC需要11-12位数字',
            icon: 'none'
          });
          return false;
        }
        break;
    }
    return true;
  },

  // 绘制条码
  drawBarcode() {
    const { inputText, barcodeWidth, barcodeHeight, typeIndex, barcodeTypes } = this.data;
    const ctx = this.ctx;
    
    // 清空画布
    ctx.clearRect(0, 0, barcodeWidth, barcodeHeight);
    
    // 设置背景
    ctx.setFillStyle('#ffffff');
    ctx.fillRect(0, 0, barcodeWidth, barcodeHeight);
    
    // 简化的条码绘制逻辑
    this.drawSimpleBarcode(ctx, inputText, barcodeWidth, barcodeHeight);
    
    ctx.draw();
  },

  // 简化的条码绘制
  drawSimpleBarcode(ctx, text, width, height) {
    const barWidth = 2;
    const textHeight = 20;
    const barcodeHeight = height - textHeight;
    
    ctx.setFillStyle('#000000');
    
    // 根据文本内容生成简单的条码模式
    let x = 10;
    for (let i = 0; i < text.length && x < width - 20; i++) {
      const charCode = text.charCodeAt(i);
      const pattern = charCode % 8; // 简化的模式生成
      
      for (let j = 0; j < 8; j++) {
        if (pattern & (1 << j)) {
          ctx.fillRect(x, 10, barWidth, barcodeHeight - 20);
        }
        x += barWidth;
      }
      x += barWidth; // 字符间隔
    }
    
    // 绘制文本
    ctx.setFillStyle('#000000');
    ctx.setFontSize(12);
    ctx.setTextAlign('center');
    ctx.fillText(text, width / 2, height - 5);
  },

  // 保存到历史记录
  saveToHistory() {
    const { inputText, typeIndex, barcodeTypes, historyList } = this.data;
    const newItem = {
      id: Date.now(),
      text: inputText,
      type: barcodeTypes[typeIndex].name,
      time: this.formatTime(new Date())
    };
    
    const newHistory = [newItem, ...historyList.slice(0, 9)]; // 保留最近10条
    this.setData({
      historyList: newHistory
    });
    
    wx.setStorageSync('barcodeHistory', newHistory);
  },

  // 加载历史记录数据
  loadHistoryData() {
    try {
      const history = wx.getStorageSync('barcodeHistory') || [];
      this.setData({
        historyList: history
      });
    } catch (e) {
      console.error('加载历史记录失败', e);
    }
  },

  // 点击历史记录项
  loadHistoryItem(e) {
    const item = e.currentTarget.dataset.item;
    const typeIndex = this.data.barcodeTypes.findIndex(type => type.name === item.type);
    
    this.setData({
      inputText: item.text,
      typeIndex: typeIndex >= 0 ? typeIndex : 0
    });
  },

  // 清空历史记录
  clearHistory() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有历史记录吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            historyList: []
          });
          wx.removeStorageSync('barcodeHistory');
          wx.showToast({
            title: '已清空',
            icon: 'success'
          });
        }
      }
    });
  },

  // 保存到相册
  saveBarcode() {
    wx.canvasToTempFilePath({
      canvasId: 'barcodeCanvas',
      success: (res) => {
        wx.saveImageToPhotosAlbum({
          filePath: res.tempFilePath,
          success: () => {
            wx.showToast({
              title: '保存成功',
              icon: 'success'
            });
          },
          fail: () => {
            wx.showToast({
              title: '保存失败',
              icon: 'none'
            });
          }
        });
      },
      fail: () => {
        wx.showToast({
          title: '生成图片失败',
          icon: 'none'
        });
      }
    });
  },

  // 分享条码
  shareBarcode() {
    wx.canvasToTempFilePath({
      canvasId: 'barcodeCanvas',
      success: (res) => {
        wx.showShareImageMenu({
          path: res.tempFilePath
        });
      }
    });
  },

  // 格式化时间
  formatTime(date) {
    const year = date.getFullYear();
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const day = date.getDate().toString().padStart(2, '0');
    const hour = date.getHours().toString().padStart(2, '0');
    const minute = date.getMinutes().toString().padStart(2, '0');
    
    return `${year}-${month}-${day} ${hour}:${minute}`;
  },

  onShareAppMessage() {
    return {
      title: '条码生成器',
      path: '/pages/barcode/barcode'
    };
  }
});