<!-- pages/barcode/barcode.wxml -->
<view class="container">
    <!-- 标题 -->
    <view class="header">
        <text class="title">条码生成器</text>
    </view>
    <!-- 输入区域 -->
    <view class="input-section">
        <view class="input-group">
            <text class="label">输入内容：</text>
            <input class="input" placeholder="请输入要生成条码的内容" value="{{inputText}}" bindinput="onInputChange" />
        </view>
        <view class="input-group">
            <text class="label">条码类型：</text>
            <picker class="picker" bindchange="onTypeChange" value="{{typeIndex}}" range="{{barcodeTypes}}" range-key="name">
                <view class="picker-text">{{barcodeTypes[typeIndex].name}}</view>
            </picker>
        </view>
        <view class="input-group">
            <text class="label">条码尺寸：</text>
            <slider class="slider" min="100" max="300" value="{{barcodeWidth}}" bindchange="onSizeChange" show-value />
        </view>
    </view>
    <!-- 生成按钮 -->
    <view class="button-section">
        <button class="generate-btn" bindtap="generateBarcode" disabled="{{!inputText}}">
            生成条码
        </button>
    </view>
    <!-- 条码显示区域 -->
    <view class="barcode-section" wx:if="{{showBarcode}}">
        <view class="barcode-container">
            <canvas class="barcode-canvas" canvas-id="barcodeCanvas" style="width: {{barcodeWidth}}px; height: {{barcodeHeight}}px;"></canvas>
        </view>
        <view class="barcode-info">
            <text class="info-text">类型: {{barcodeTypes[typeIndex].name}}</text>
            <text class="info-text">内容: {{inputText}}</text>
        </view>
        <!-- 操作按钮 -->
        <view class="action-buttons">
            <button class="action-btn" bindtap="saveBarcode">保存到相册</button>
            <button class="action-btn secondary" bindtap="shareBarcode">分享条码</button>
        </view>
    </view>
    <!-- 历史记录 -->
    <view class="history-section" wx:if="{{historyList.length > 0}}">
        <view class="section-title">
            <text>历史记录</text>
            <text class="clear-btn" bindtap="clearHistory">清空</text>
        </view>
        <scroll-view class="history-list" scroll-y>
            <view class="history-item" wx:for="{{historyList}}" wx:key="id" bindtap="loadHistoryItem" data-item="{{item}}">
                <view class="history-content">
                    <text class="history-text">{{item.text}}</text>
                    <text class="history-type">{{item.type}}</text>
                </view>
                <text class="history-time">{{item.time}}</text>
            </view>
        </scroll-view>
    </view>
</view>