{"description": "HiddenTextTool - 隐藏文字生成工具", "packOptions": {"ignore": [{"value": ".eslintrc.js", "type": "file"}], "include": []}, "setting": {"urlCheck": false, "es6": true, "enhance": true, "postcss": true, "preloadBackgroundData": false, "minified": true, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": true, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "lazyloadPlaceholderEnable": false, "useMultiFrameRuntime": true, "useApiHook": true, "useApiHostProcess": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "enableEngineNative": false, "useIsolateContext": false, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": true, "disableUseStrict": false, "minifyWXML": true, "showES6CompileOption": false, "useCompilerPlugins": false, "swc": true, "disableSWC": false, "compileWorklet": true, "localPlugins": false, "condition": false}, "compileType": "miniprogram", "libVersion": "3.8.12", "appid": "wx5bcf436a452c53cd", "projectname": "HiddenTextTool", "condition": {}, "editorSetting": {"tabIndent": "auto", "tabSize": 2}, "simulatorPluginLibVersion": {}}