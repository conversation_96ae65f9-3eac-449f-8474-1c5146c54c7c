# HiddenTextTool - 微信小程序版本

一个基于Canvas的隐藏文字生成工具，用于创建带有干扰线图案的文字图像，让文字在视觉上更加隐蔽。

## 🚀 功能特性

### 文字层设置
- ✅ 多行文字输入
- ✅ 字体大小、行间距、字间距调节
- ✅ 文字颜色、背景颜色选择
- ✅ 对齐方式（左、中、右）
- ✅ 字体加粗、文字模糊效果
- ✅ 内边距控制

### 干扰线层系统
- ✅ 锯齿线图案（规律的锯齿形状，水平和垂直方向）
- ✅ 波浪线图案（平滑的正弦波形）
- ✅ 网格图案（规整的网格线条）
- ✅ 点阵图案（规律的点状分布）
- ✅ 可调节参数：锯齿长度、高度、线宽、间隔
- ✅ 支持描边效果
- ✅ 插件化架构，易于扩展

### 交互功能
- ✅ 画布缩放和平移
- ✅ 文字区域拖拽移动
- ✅ 实时预览
- ✅ 固定375x375尺寸

### 导出功能
- ✅ 多种图片格式（PNG、JPEG）
- ✅ 多种导出尺寸
- ✅ 质量调节
- ✅ 干扰线开启开关

## 📱 使用方法

### 基本操作
1. **输入文字**：在文字内容区域输入要隐藏的文字，支持多行输入
2. **调整文字样式**：通过滑块和开关调整字体大小、颜色、对齐方式等
3. **设置干扰线**：开启干扰线功能，调整锯齿参数来控制隐藏效果
4. **拖拽定位**：在画布上拖拽文字到合适位置
5. **预览导出**：点击预览查看效果，满意后导出到相册

### 文字设置说明
- **字体大小**：12-48px，控制文字显示大小
- **行间距**：1.0-3.0倍，调整多行文字的行距
- **字间距**：0-10px，调整字符之间的间距
- **对齐方式**：左对齐、居中对齐、右对齐
- **文字颜色**：支持十六进制颜色码，如 #333333
- **背景颜色**：支持十六进制颜色码，如 #ffffff
- **加粗**：开启后文字显示为粗体
- **模糊**：开启后文字带有轻微模糊效果

### 干扰线设置说明
- **启用干扰线**：总开关，控制是否显示干扰线
- **锯齿长度**：5-30px，控制锯齿线的长度
- **锯齿高度**：2-15px，控制锯齿线的高度变化
- **线宽**：1-5px，控制干扰线的粗细
- **线间隔**：10-50px，控制干扰线之间的距离
- **线颜色**：支持十六进制颜色码，建议使用浅色

### 导出设置说明
- **图片格式**：PNG（支持透明）或 JPEG（文件更小）
- **导出尺寸**：375x375、750x750、1125x1125
- **图片质量**：0.1-1.0，仅对JPEG格式有效

## 🛠 技术架构

### 核心文件结构
```
├── pages/index/
│   ├── index.js      # 主页面逻辑
│   ├── index.wxml    # 页面结构
│   └── index.wxss    # 页面样式
├── utils/
│   ├── patterns/
│   │   ├── IInterferencePattern.js    # 基础接口
│   │   ├── ZigzagPattern.js          # 锯齿线图案
│   │   ├── WavePattern.js            # 波浪线图案
│   │   ├── GridPattern.js            # 网格图案
│   │   ├── DotPattern.js             # 点阵图案
│   │   └── DiagonalPattern.js        # 对角线图案（扩展示例）
│   ├── PatternManager.js             # 图案管理器
│   └── interferencePatterns.js       # 统一入口
├── docs/
│   └── pattern-extension-guide.md    # 图案扩展指南
├── examples/
│   └── pattern-demo.js               # 使用示例
├── app.js            # 小程序入口
├── app.json          # 小程序配置
└── README.md         # 说明文档
```

### 干扰线图案系统
采用插件化架构，支持多种干扰线图案：

- **ZigzagPattern**：锯齿线图案（当前唯一实现的图案类型）

后续计划添加的图案类型：
- **WavePattern**：波浪线图案（开发中）
- **GridPattern**：网格图案（计划中）
- **DotPattern**：点阵图案（计划中）
- **DiagonalPattern**：对角线图案（计划中）

可通过 `PatternManager` 轻松扩展新的图案类型。

### Canvas绘制流程
1. 清空画布
2. 绘制背景色
3. 绘制干扰线图案
4. 绘制文字内容
5. 应用特效（模糊等）

## 🎨 自定义扩展

### 添加新的干扰线图案
```javascript
// 继承 InterferencePattern 基类
class CustomPattern extends InterferencePattern {
  constructor(options = {}) {
    super({
      // 自定义默认参数
      customParam: 10,
      ...options
    });
  }

  draw(ctx, width, height) {
    // 实现自定义绘制逻辑
    const { lineWidth, lineColor, customParam } = this.options;
    ctx.setStrokeStyle(lineColor);
    ctx.setLineWidth(lineWidth);
    // ... 绘制代码
  }
}

// 注册到管理器
patternManager.register('custom', CustomPattern);
```

### 修改颜色主题
在 `index.wxss` 中修改 CSS 变量：
```css
:root {
  --primary-color: #667eea;
  --success-color: #11998e;
  --background-color: #f5f5f5;
}
```

## 📝 注意事项

1. **权限设置**：导出图片需要用户授权相册权限
2. **性能优化**：大量干扰线可能影响绘制性能，建议适度使用
3. **颜色格式**：颜色输入支持 #RGB 和 #RRGGBB 格式
4. **文字长度**：过长的文字可能超出画布范围，建议分行输入
5. **兼容性**：需要微信基础库 2.9.0 及以上版本

## 🔧 开发调试

### 本地开发
1. 使用微信开发者工具打开项目
2. 在模拟器中测试各项功能
3. 真机调试验证导出功能

### 常见问题
- **画布不显示**：检查 canvas-id 是否正确
- **导出失败**：检查相册权限和文件路径
- **文字模糊**：调整 canvas 的 width/height 属性
- **干扰线不显示**：检查线颜色是否与背景色相同

## 📄 许可证

MIT License - 可自由使用和修改