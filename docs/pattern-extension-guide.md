# 干扰线图案扩展指南

本文档介绍如何为 HiddenTextTool 添加新的干扰线图案类型。

## 目录结构

```
utils/
├── PatternManager.js          # 图案管理器
└── patterns/
    ├── IInterferencePattern.js # 图案接口
    ├── BasePattern.js         # 图案基类
    ├── ZigzagPattern.js       # 锯齿线图案
    ├── WavePattern.js         # 波浪线图案
    ├── GridPattern.js         # 网格图案
    ├── DotPattern.js          # 点阵图案
    └── DiagonalPattern.js     # 对角线图案
```

## 创建新图案类型

### 步骤 1: 创建新的图案类文件

在 `utils/patterns/` 目录下创建一个新的 JS 文件，例如 `MyNewPattern.js`：

```javascript
/**
 * utils/patterns/MyNewPattern.js
 * 我的新图案
 */

const BasePattern = require('./BasePattern');

/**
 * 我的新图案
 */
class MyNewPattern extends BasePattern {
  /**
   * 构造函数
   * @param {Object} options - 配置选项
   */
  constructor(options = {}) {
    super({
      // 定义默认参数
      myParam1: 10,
      myParam2: 20,
      ...options
    });
  }

  /**
   * 绘制图案
   * @param {CanvasContext} ctx - 画布上下文
   * @param {number} width - 画布宽度
   * @param {number} height - 画布高度
   */
  draw(ctx, width, height) {
    const { lineWidth, lineColor, myParam1, myParam2 } = this.options;
    
    ctx.setStrokeStyle(lineColor);
    ctx.setLineWidth(lineWidth);

    // 实现自定义绘制逻辑
    // ...

    // 示例：绘制一些随机线条
    for (let i = 0; i < myParam1; i++) {
      ctx.beginPath();
      ctx.moveTo(Math.random() * width, Math.random() * height);
      ctx.lineTo(Math.random() * width, Math.random() * height);
      ctx.stroke();
    }
  }
}

module.exports = MyNewPattern;
```

### 步骤 2: 在 PatternManager 中注册新图案

修改 `utils/PatternManager.js` 文件，导入并注册新图案：

```javascript
// 导入所有图案类型
const ZigzagPattern = require('./patterns/ZigzagPattern');
// ... 其他导入
const MyNewPattern = require('./patterns/MyNewPattern');

class PatternManager {
  // ...

  registerDefaultPatterns() {
    this.register('zigzag', ZigzagPattern);
    // ... 其他注册
    this.register('myNew', MyNewPattern);
  }

  // ...
}
```

### 步骤 3: 在界面中添加新图案选项

修改 `pages/index/index.js` 文件，更新图案选项：

```javascript
data: {
  // ...
  patternOptions: ['锯齿线', '波浪线', '网格', '点阵', '对角线', '我的新图案'],
  // ...
}

// ...

onPatternChange(e) {
  const patternNames = ['zigzag', 'wave', 'grid', 'dot', 'diagonal', 'myNew'];
  // ...
}
```

## 图案参数指南

创建新图案时，可以使用以下通用参数，也可以添加自定义参数：

### 通用参数

- `lineWidth`: 线宽
- `lineColor`: 线颜色
- `spacing`: 线间距

### 特定图案参数示例

#### 锯齿线 (ZigzagPattern)

- `zigzagLength`: 锯齿长度
- `zigzagHeight`: 锯齿高度
- `direction`: 方向 ('horizontal', 'vertical', 'both')

#### 波浪线 (WavePattern)

- `amplitude`: 波浪振幅
- `frequency`: 波浪频率
- `direction`: 方向 ('horizontal', 'vertical', 'both')

#### 网格 (GridPattern)

- `gridSize`: 网格大小
- `opacity`: 透明度

#### 点阵 (DotPattern)

- `dotSize`: 点大小
- `dotSpacing`: 点间距
- `opacity`: 透明度

#### 对角线 (DiagonalPattern)

- `lineSpacing`: 线间距
- `direction`: 方向 ('leftToRight', 'rightToLeft', 'both')

## 最佳实践

1. **参数命名**: 使用描述性名称，避免与其他图案参数冲突
2. **默认值**: 为所有参数提供合理的默认值
3. **文档**: 为类和方法添加详细的 JSDoc 注释
4. **性能**: 注意绘制性能，避免过于复杂的计算
5. **可配置性**: 尽可能让图案的各个方面都可配置

## 示例：创建星形图案

```javascript
/**
 * utils/patterns/StarPattern.js
 * 星形图案
 */

const BasePattern = require('./BasePattern');

/**
 * 星形图案
 */
class StarPattern extends BasePattern {
  constructor(options = {}) {
    super({
      starSize: 15,
      starCount: 50,
      opacity: 0.5,
      ...options
    });
  }

  draw(ctx, width, height) {
    const { lineColor, starSize, starCount, opacity } = this.options;
    
    ctx.setFillStyle(lineColor);
    ctx.setGlobalAlpha(opacity);

    // 绘制随机分布的星星
    for (let i = 0; i < starCount; i++) {
      const x = Math.random() * width;
      const y = Math.random() * height;
      this.drawStar(ctx, x, y, starSize, 5);
    }

    ctx.setGlobalAlpha(1);
  }

  // 绘制五角星
  drawStar(ctx, x, y, size, points = 5) {
    ctx.beginPath();
    for (let i = 0; i < points * 2; i++) {
      const radius = i % 2 === 0 ? size : size / 2;
      const angle = (Math.PI * i) / points;
      const pointX = x + radius * Math.sin(angle);
      const pointY = y - radius * Math.cos(angle);
      
      if (i === 0) {
        ctx.moveTo(pointX, pointY);
      } else {
        ctx.lineTo(pointX, pointY);
      }
    }
    ctx.closePath();
    ctx.fill();
  }
}

module.exports = StarPattern;
```